"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx":
/*!**************************************************************************!*\
  !*** ./src/components/filter/components/crew-dropdown/crew-dropdown.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CrewDropdown = (param)=>{\n    let { label = \"Trainer\", value, onChange, controlClasses = \"default\", placeholder = \"Trainer\", isClearable = false, filterByTrainingSessionMemberId = 0, trainerIdOptions = [], memberIdOptions = [], multi = false, offline = false, vesselID = 0, disabled = false } = param;\n    _s();\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedValue, setSelectedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(multi ? [] : null);\n    // Debug selectedValue changes\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [allCrewList, setAllCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [imCrew, setImCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // if this is a crew member or not\n    ;\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const [allFetchedData, setAllFetchedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentFilter, setCurrentFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentOffset, setCurrentOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const processCompleteData = (completeData)=>{\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? completeData.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : completeData;\n        if (vesselCrewList) {\n            if (imCrew && pathname === \"/reporting\") {\n                // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user\n                const userId = localStorage.getItem(\"userId\");\n                const filteredCrewList = vesselCrewList.filter((crew)=>{\n                    return crew.id === userId;\n                });\n                setCrewList(filteredCrewList);\n            } else {\n                setCrewList(vesselCrewList);\n            }\n            setAllCrewList(vesselCrewList);\n        }\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_6__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            const pageInfo = response.readSeaLogsMembers.pageInfo;\n            // Accumulate data from all pages\n            const newCompleteData = [\n                ...allFetchedData,\n                ...data\n            ];\n            setAllFetchedData(newCompleteData);\n            // If there are more pages, fetch the next page\n            if (pageInfo.hasNextPage) {\n                const newOffset = currentOffset + data.length;\n                setCurrentOffset(newOffset);\n                querySeaLogsMembersList({\n                    variables: {\n                        filter: currentFilter,\n                        offset: newOffset,\n                        limit: 100\n                    }\n                });\n                return; // Don't process the crew list yet, wait for all data\n            }\n            // All data has been fetched, now process the complete dataset\n            processCompleteData(newCompleteData);\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        let filter = {\n            isArchived: {\n                eq: false\n            }\n        };\n        if (filterByTrainingSessionMemberId > 0) {\n            filter = {\n                ...filter,\n                trainingSessions: {\n                    members: {\n                        id: {\n                            contains: filterByTrainingSessionMemberId\n                        }\n                    }\n                }\n            };\n        }\n        if (offline) {\n            // querySeaLogsMembersList\n            const allCrews = await seaLogsMemberModel.getAll();\n            const data = allCrews.filter((crew)=>{\n                if (filterByTrainingSessionMemberId > 0) {\n                    return crew.isArchived === false && crew.trainingSessions.nodes.some((trainingSession)=>trainingSession.members.nodes.some((member)=>member.id === filterByTrainingSessionMemberId));\n                } else {\n                    return crew.isArchived === false;\n                }\n            });\n            if (data) {\n                if (imCrew && pathname === \"/reporting\") {\n                    // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user\n                    const userId = localStorage.getItem(\"userId\");\n                    const filteredCrewList = data.filter((crew)=>{\n                        return crew.id === userId;\n                    });\n                    setCrewList(filteredCrewList);\n                } else {\n                    setCrewList(data);\n                }\n                setAllCrewList(data);\n            }\n        } else {\n            // Reset accumulated data and set current filter for pagination\n            setAllFetchedData([]);\n            setCurrentOffset(0);\n            setCurrentFilter(filter);\n            await querySeaLogsMembersList({\n                variables: {\n                    filter: filter,\n                    offset: 0,\n                    limit: 100\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            setImCrew((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.isCrew)() || false);\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value && crewList) {\n            const crew = crewList.find((crew)=>crew.id === value);\n            if (crew) {\n                const newSelectedValue = {\n                    value: crew.id,\n                    label: \"\".concat(crew.firstName || \"\", \" \").concat(crew.surname || \"\"),\n                    profile: {\n                        firstName: crew.firstName,\n                        surname: crew.surname,\n                        avatar: null\n                    }\n                };\n                setSelectedValue(newSelectedValue);\n            }\n        } else if (!value) {\n            // Reset to null when no value is provided to show placeholder\n            setSelectedValue(null);\n        }\n    }, [\n        value,\n        crewList,\n        placeholder\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (trainerIdOptions.length > 0 && allCrewList.length > 0) {\n            const filteredCrewList = allCrewList.filter((crew)=>{\n                return trainerIdOptions.includes(crew.id);\n            });\n            setCrewList(filteredCrewList);\n        }\n    }, [\n        trainerIdOptions,\n        allCrewList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (memberIdOptions.length > 0 && allCrewList.length > 0) {\n            const filteredCrewList = allCrewList.filter((crew)=>{\n                return memberIdOptions.includes(crew.id);\n            });\n            setCrewList(filteredCrewList);\n        } else if (allCrewList.length > 0) {\n            // Use all crew members if no specific memberIdOptions are provided\n            setCrewList(allCrewList);\n        }\n    }, [\n        memberIdOptions,\n        allCrewList,\n        placeholder\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_5__.Combobox, {\n        options: crewList === null || crewList === void 0 ? void 0 : crewList.map((crew)=>({\n                value: crew.id,\n                label: \"\".concat(crew.firstName || \"\", \" \").concat(crew.surname || \"\"),\n                profile: {\n                    firstName: crew.firstName,\n                    surname: crew.surname,\n                    avatar: null\n                }\n            })),\n        value: selectedValue,\n        onChange: (selectedOption)=>{\n            // selectedOption is the Option object from Combobox\n            setSelectedValue(selectedOption);\n            const dataToPass = multi ? selectedOption : selectedOption || null // Pass full object instead of just value\n            ;\n            // Pass the crew data to the parent component\n            onChange(dataToPass);\n        },\n        //label={label}\n        multi: multi,\n        //labelClassName=\"w-full\"\n        isLoading: !crewList,\n        placeholder: placeholder,\n        disabled: disabled\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\crew-dropdown\\\\crew-dropdown.tsx\",\n        lineNumber: 207,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewDropdown, \"naTRpStfbcmP23BdIR1Qcr1mfWA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = CrewDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\n"));

/***/ })

});