"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/page",{

/***/ "(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx":
/*!**************************************************************************!*\
  !*** ./src/components/filter/components/crew-dropdown/crew-dropdown.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CrewDropdown = (param)=>{\n    let { label = \"Trainer\", value, onChange, controlClasses = \"default\", placeholder = \"Trainer\", isClearable = false, filterByTrainingSessionMemberId = 0, trainerIdOptions = [], memberIdOptions = [], multi = false, offline = false, vesselID = 0, disabled = false } = param;\n    _s();\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedValue, setSelectedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(multi ? [] : null);\n    // Debug selectedValue changes\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [allCrewList, setAllCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [imCrew, setImCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // if this is a crew member or not\n    ;\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const [allFetchedData, setAllFetchedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentFilter, setCurrentFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentOffset, setCurrentOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const processCompleteData = (completeData)=>{\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? completeData.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : completeData;\n        if (vesselCrewList) {\n            if (imCrew && pathname === \"/reporting\") {\n                // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user\n                const userId = localStorage.getItem(\"userId\");\n                const filteredCrewList = vesselCrewList.filter((crew)=>{\n                    return crew.id === userId;\n                });\n                setCrewList(filteredCrewList);\n            } else {\n                setCrewList(vesselCrewList);\n            }\n            setAllCrewList(vesselCrewList);\n        }\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_6__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            const pageInfo = response.readSeaLogsMembers.pageInfo;\n            // Accumulate data from all pages\n            const newCompleteData = [\n                ...allFetchedData,\n                ...data\n            ];\n            setAllFetchedData(newCompleteData);\n            // If there are more pages, fetch the next page\n            if (pageInfo.hasNextPage) {\n                const newOffset = currentOffset + data.length;\n                setCurrentOffset(newOffset);\n                querySeaLogsMembersList({\n                    variables: {\n                        filter: currentFilter,\n                        offset: newOffset,\n                        limit: 100\n                    }\n                });\n                return; // Don't process the crew list yet, wait for all data\n            }\n            // All data has been fetched, now process the complete dataset\n            processCompleteData(newCompleteData);\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        let filter = {\n            isArchived: {\n                eq: false\n            }\n        };\n        if (filterByTrainingSessionMemberId > 0) {\n            filter = {\n                ...filter,\n                trainingSessions: {\n                    members: {\n                        id: {\n                            contains: filterByTrainingSessionMemberId\n                        }\n                    }\n                }\n            };\n        }\n        if (offline) {\n            // querySeaLogsMembersList\n            const allCrews = await seaLogsMemberModel.getAll();\n            const data = allCrews.filter((crew)=>{\n                if (filterByTrainingSessionMemberId > 0) {\n                    return crew.isArchived === false && crew.trainingSessions.nodes.some((trainingSession)=>trainingSession.members.nodes.some((member)=>member.id === filterByTrainingSessionMemberId));\n                } else {\n                    return crew.isArchived === false;\n                }\n            });\n            if (data) {\n                if (imCrew && pathname === \"/reporting\") {\n                    // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user\n                    const userId = localStorage.getItem(\"userId\");\n                    const filteredCrewList = data.filter((crew)=>{\n                        return crew.id === userId;\n                    });\n                    setCrewList(filteredCrewList);\n                } else {\n                    setCrewList(data);\n                }\n                setAllCrewList(data);\n            }\n        } else {\n            // Reset accumulated data and set current filter for pagination\n            setAllFetchedData([]);\n            setCurrentOffset(0);\n            setCurrentFilter(filter);\n            await querySeaLogsMembersList({\n                variables: {\n                    filter: filter,\n                    offset: 0,\n                    limit: 100\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            setImCrew((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.isCrew)() || false);\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value && crewList) {\n            const crew = crewList.find((crew)=>crew.id === value);\n            if (crew) {\n                const newSelectedValue = {\n                    value: crew.id,\n                    label: \"\".concat(crew.firstName || \"\", \" \").concat(crew.surname || \"\"),\n                    profile: {\n                        firstName: crew.firstName,\n                        surname: crew.surname,\n                        avatar: null\n                    }\n                };\n                setSelectedValue(newSelectedValue);\n            }\n        } else if (!value) {\n            // Reset to null when no value is provided to show placeholder\n            setSelectedValue(null);\n        }\n    }, [\n        value,\n        crewList,\n        placeholder\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (trainerIdOptions.length > 0 && allCrewList.length > 0) {\n            const filteredCrewList = allCrewList.filter((crew)=>{\n                return trainerIdOptions.includes(crew.id);\n            });\n            setCrewList(filteredCrewList);\n        }\n    }, [\n        trainerIdOptions,\n        allCrewList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (memberIdOptions.length > 0 && allCrewList.length > 0) {\n            const filteredCrewList = allCrewList.filter((crew)=>{\n                return memberIdOptions.includes(crew.id);\n            });\n            setCrewList(filteredCrewList);\n        } else if (allCrewList.length > 0) {\n            // Use all crew members if no specific memberIdOptions are provided\n            setCrewList(allCrewList);\n        }\n    }, [\n        memberIdOptions,\n        allCrewList,\n        placeholder\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_5__.Combobox, {\n        options: crewList === null || crewList === void 0 ? void 0 : crewList.map((crew)=>({\n                value: crew.id,\n                label: \"\".concat(crew.firstName || \"\", \" \").concat(crew.surname || \"\"),\n                profile: {\n                    firstName: crew.firstName,\n                    surname: crew.surname,\n                    avatar: null\n                }\n            })),\n        value: selectedValue,\n        onChange: (selectedOption)=>{\n            // selectedOption is the Option object from Combobox\n            setSelectedValue(selectedOption);\n            // Debug the data being passed to parent\n            console.log(\"CrewDropdown onChange - selectedOption:\", selectedOption);\n            const dataToPass = multi ? selectedOption : selectedOption || null // Pass full object instead of just value\n            ;\n            console.log(\"CrewDropdown onChange - dataToPass:\", dataToPass);\n            // Pass the crew data to the parent component\n            onChange(dataToPass);\n        },\n        //label={label}\n        multi: multi,\n        //labelClassName=\"w-full\"\n        isLoading: !crewList,\n        placeholder: placeholder,\n        disabled: disabled\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\crew-dropdown\\\\crew-dropdown.tsx\",\n        lineNumber: 207,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewDropdown, \"naTRpStfbcmP23BdIR1Qcr1mfWA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = CrewDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\n"));

/***/ })

});