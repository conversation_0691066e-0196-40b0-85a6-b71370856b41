"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx":
/*!**************************************************!*\
  !*** ./src/app/ui/crew-training/create/form.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew-training/create/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TrainingForm = (param)=>{\n    let { trainingID = 0, memberId = 0, trainingTypeId = 0, vesselId = 0 } = param;\n    var _rawTraining_VesselID, _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _vessels_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Date());\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [vesselID, setVesselID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(vesselId);\n    const [fieldImages, setFieldImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypes)(setTrainingTypes);\n    const [getFieldImages] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_SECTION_MEMBER_IMAGES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCaptureImages.nodes;\n            if (data) {\n                setFieldImages(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getFieldImages error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    }, []);\n    const refreshImages = async ()=>{\n        await getFieldImages({\n            variables: {\n                filter: {\n                    trainingSessionID: {\n                        eq: trainingID\n                    }\n                }\n            }\n        });\n    };\n    const handleSetTraining = (training)=>{\n        var _training_procedureFields;\n        const tDate = new Date(training.date);\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.date).format(\"YYYY-MM-DD\"),\n            Members: training.members.nodes.map((m)=>m.id),\n            TrainerID: training.trainer.id,\n            TrainingSummary: training.trainingSummary,\n            TrainingTypes: training.trainingTypes.nodes.map((t)=>t.id),\n            // VesselID: training.vessel.id,\n            VesselID: training.vesselID\n        };\n        setRawTraining(training);\n        setTraining(trainingData);\n        setContent(training.trainingSummary);\n        const members = training.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        const vesselCrewIds = training.vessel.seaLogsMembers.nodes.map((slm)=>+slm.id);\n        const vesselCrews = members.filter((m)=>vesselCrewIds.includes(+m.value));\n        setSelectedMemberList(vesselCrews);\n        const signatures = training.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n        // Initialize buffer with existing procedure field data for updates\n        if ((_training_procedureFields = training.procedureFields) === null || _training_procedureFields === void 0 ? void 0 : _training_procedureFields.nodes) {\n            const existingProcedureChecks = training.procedureFields.nodes.map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    status: field.status === \"Ok\"\n                }));\n            setBufferProcedureCheck(existingProcedureChecks);\n            const existingFieldComments = training.procedureFields.nodes.filter((field)=>field.comment).map((field)=>({\n                    fieldId: field.customisedComponentFieldID,\n                    comment: field.comment\n                }));\n            setBufferFieldComment(existingFieldComments);\n        }\n    };\n    const handleSetVessels = (data)=>{\n        const activeVessels = data === null || data === void 0 ? void 0 : data.filter((vessel)=>!vessel.archived);\n        const formattedData = [\n            {\n                label: \"Other\",\n                value: \"Other\"\n            },\n            {\n                label: \"Desktop/shore\",\n                value: \"Onshore\"\n            },\n            ...activeVessels.map((vessel)=>({\n                    value: vessel.id,\n                    label: vessel.title\n                }))\n        ];\n        setVessels(formattedData);\n    };\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_29__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingSessionByID)(trainingID, handleSetTraining);\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                router.push(\"/crew-training\");\n            } else {\n                console.error(\"mutationCreateUser error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                // Handle procedure checks for updates\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields, _bufferFieldComment_find;\n                        const existingField = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((field)=>field.customisedComponentFieldID === procedureField.fieldId);\n                        return {\n                            id: existingField === null || existingField === void 0 ? void 0 : existingField.id,\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        if (procedureField.id) {\n                            // Update existing field\n                            updateCustomisedComponentFieldData({\n                                variables: {\n                                    input: procedureField\n                                }\n                            });\n                        } else {\n                            // Create new field\n                            const { id, ...createInput } = procedureField;\n                            createCustomisedComponentFieldData({\n                                variables: {\n                                    input: createInput\n                                }\n                            });\n                        }\n                    });\n                }\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n                if (+memberId > 0) {\n                    router.push(\"/crew/info?id=\".concat(memberId));\n                } else if (+vesselId > 0) {\n                    router.push(\"/vessel/info?id=\".concat(vesselId));\n                } else {\n                    router.push(\"/crew-training\");\n                }\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        const { data } = await readOneTrainingSessionDue({\n            variables: variables\n        });\n        onCompleted(data.readOneTrainingSessionDue);\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    await mutationCreateTrainingSessionDue(variables);\n                } else {\n                    await mutationUpdateTrainingSessionDue(variables);\n                }\n            }));\n        }\n    };\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(errors.TrainingTypes || errors.TrainerID || errors.VesselID || errors.Date || \"Please fix the errors above\");\n            return;\n        }\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\"\n        };\n        if (trainingID === 0) {\n            await mutationCreateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        } else {\n            await mutationUpdateTrainingSession({\n                variables: {\n                    input: input\n                }\n            });\n        }\n    };\n    // var signatureCount = 0\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        await queryGetMemberTrainingSignatures({\n            variables: {\n                filter: {\n                    memberID: {\n                        eq: signature.MemberID\n                    },\n                    trainingSessionID: {\n                        in: TrainingID\n                    }\n                }\n            }\n        }).then((response)=>{\n            const data = response.data.readMemberTraining_Signatures.nodes;\n            if (data.length > 0) {\n                mutationUpdateMemberTrainingSignature({\n                    variables: {\n                        input: {\n                            id: data[0].id,\n                            memberID: signature.MemberID,\n                            signatureData: signature.SignatureData,\n                            trainingSessionID: TrainingID\n                        }\n                    }\n                });\n            } else {\n                if (signature.SignatureData) {\n                    mutationCreateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"mutationGetMemberTrainingSignatures error\", error);\n        });\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_9__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_8__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(date && new Date(date.toString()));\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        console.log(\"handleTrainerChange - received trainer:\", trainer);\n        if (!trainer) return; // Add early return if trainer is null\n        // With the updated CrewDropdown, trainer should now be a full object with label and value\n        // But we'll still handle the legacy case where it might just be an ID\n        const trainerId = trainer.value || trainer;\n        // Create a proper trainer object with the correct label format\n        const trainerObject = trainer.label ? trainer // Use the object as is if it has a label\n         : {\n            value: trainerId,\n            label: trainer.profile ? \"\".concat(trainer.profile.firstName || \"\", \" \").concat(trainer.profile.surname || \"\") : \"Trainer \".concat(trainerId)\n        };\n        console.log(\"handleTrainerChange - created trainerObject:\", trainerObject);\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainerId);\n        const members = Array.from(membersSet);\n        // Update the training state\n        setTraining({\n            ...training,\n            TrainerID: trainerId,\n            Members: members\n        });\n        // Add the trainer to the selectedMemberList if not already present\n        const trainerExists = selectedMemberList.some((member)=>member.value === trainerId);\n        if (!trainerExists) {\n            console.log(\"Adding trainer to selectedMemberList:\", trainerObject);\n            setSelectedMemberList([\n                ...selectedMemberList,\n                trainerObject\n            ]);\n            setSignatureMembers([\n                ...signatureMembers,\n                {\n                    MemberID: +trainerId,\n                    SignatureData: null\n                }\n            ]);\n        }\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        setTraining({\n            ...training,\n            TrainingTypes: trainingTypes.map((item)=>item.value)\n        });\n    };\n    /* const handleTrainingLocationChange = (vessel: any) => {\r\n        setTraining({\r\n            ...training,\r\n            VesselID: vessel.isVessel ? vessel.value : 0,\r\n            TrainingLocationID: !vessel.isVessel ? vessel.value : 0,\r\n        })\r\n    } */ const handleMemberChange = (members)=>{\n        console.log(\"handleMemberChange - received members:\", members);\n        const signatures = signatureMembers.filter((item)=>members.some((m)=>+m.value === item.MemberID));\n        setTraining({\n            ...training,\n            Members: members.map((item)=>item.value)\n        });\n        console.log(\"handleMemberChange - setting selectedMemberList to:\", members);\n        setSelectedMemberList(members);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (e, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (e) {\n            if (index !== -1) {\n                if (e.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = e;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: e\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel ? typeof vessel === \"object\" && !Array.isArray(vessel) ? vessel.value : 0 : 0\n        });\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(training)) {\n            const vid = vesselId > 0 || isNaN(parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10)) ? vesselId : parseInt(training === null || training === void 0 ? void 0 : training.VesselID, 10);\n            setVesselID(vid);\n        }\n    }, [\n        vesselId,\n        training\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.getPermissions);\n    }, []);\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 870,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n            lineNumber: 872,\n            columnNumber: 13\n        }, undefined);\n    }\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            var _this;\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: (_this = [\n                    ...type.customisedComponentField.nodes\n                ]) === null || _this === void 0 ? void 0 : _this.sort((a, b)=>a.sortOrder - b.sortOrder)\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n        setBufferProcedureCheck([\n            ...procedureCheck,\n            {\n                fieldId: field.id,\n                status: status\n            }\n        ]);\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        // Always use buffer system for consistency, whether creating or updating\n        const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n        setBufferFieldComment([\n            ...fieldComment,\n            {\n                fieldId: currentField.id,\n                comment: currentComment\n            }\n        ]);\n        setOpenCommentAlert(false);\n    };\n    console.log(\"member\", selectedMemberList);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.ListHeader, {\n                title: \"\".concat(trainingID === 0 ? \"New\" : \"Edit\", \"\\n                training\\n                session\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 973,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                className: \"mb-2.5 mx-4\",\n                children: !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_5__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 980,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3 md:col-span-1 my-4 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                            children: \"Training details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        training && trainingTypes.filter((type)=>{\n                                            var _training_TrainingTypes;\n                                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                        }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                                            onClick: ()=>setOpenViewProcedure(true),\n                                            children: \"View procedures\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 993,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3 md:col-span-2 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full my-4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                            label: \"Trainer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                                                vesselID: vesselID,\n                                                                onChange: handleTrainerChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1004,\n                                                            columnNumber: 41\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"text-destructive\",\n                                                            children: hasFormErrors && formErrors.TrainerID\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1011,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full md:mt-4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                                            onChange: handleTrainingTypeChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1017,\n                                                            columnNumber: 41\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"text-red-vivid-500\",\n                                                            children: hasFormErrors && formErrors.TrainingTypes\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1021,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1016,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 1002,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full mt-4 flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                    children: \"Crew\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    value: memberId > 0 ? [\n                                                        memberId.toString()\n                                                    ] : ((training === null || training === void 0 ? void 0 : training.Members) || []).filter((id)=>id !== undefined && id !== null),\n                                                    vesselID: vesselID,\n                                                    onChange: handleMemberChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                                            className: \"my-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 1045,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex w-full gap-4 mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full \",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            mode: \"single\",\n                                                            onChange: handleTrainingDateChange,\n                                                            value: new Date(trainingDate)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1052,\n                                                            columnNumber: 41\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"text-destructive\",\n                                                            children: hasFormErrors && formErrors.Date\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1057,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1047,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        vessels && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                                            options: vessels.map((vessel)=>({\n                                                                    label: vessel.label,\n                                                                    value: vessel.value\n                                                                })),\n                                                            defaultValues: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_VesselID = rawTraining.VesselID) === null || _rawTraining_VesselID === void 0 ? void 0 : _rawTraining_VesselID.toString() : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? \"Desktop/shore\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? \"Other\" : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.id.toString() : vesselId ? {\n                                                                label: (_vessels_find = vessels.find((vessel)=>vessel.value === vesselId)) === null || _vessels_find === void 0 ? void 0 : _vessels_find.label,\n                                                                value: vesselId.toString()\n                                                            } : null,\n                                                            isLoading: rawTraining,\n                                                            onChange: handleTrainingVesselChange,\n                                                            placeholder: \"Select location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1063,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"text-destructive\",\n                                                            children: hasFormErrors && formErrors.VesselID\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1061,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 1046,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-3 md:col-span-2\",\n                                            children: [\n                                                getProcedures().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-8\",\n                                                    children: getProcedures().map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.CheckField, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                    label: type.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 1128,\n                                                                    columnNumber: 57\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.CheckFieldContent, {\n                                                                    children: type.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_23__.DailyCheckField, {\n                                                                            displayField: field.status === \"Required\",\n                                                                            displayDescription: field.description,\n                                                                            displayLabel: field.fieldName,\n                                                                            inputId: field.id,\n                                                                            handleNoChange: ()=>handleProcedureChecks(field, type, false),\n                                                                            defaultNoChecked: getFieldStatus(field) === \"Not_Ok\",\n                                                                            handleYesChange: ()=>handleProcedureChecks(field, type, true),\n                                                                            defaultYesChecked: getFieldStatus(field) === \"Ok\",\n                                                                            commentAction: ()=>showCommentPopup(field),\n                                                                            comment: getComment(field),\n                                                                            displayImage: trainingID > 0,\n                                                                            fieldImages: fieldImages,\n                                                                            onImageUpload: refreshImages,\n                                                                            sectionData: {\n                                                                                id: trainingID,\n                                                                                sectionName: \"trainingSessionID\"\n                                                                            }\n                                                                        }, field.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                            lineNumber: 1136,\n                                                                            columnNumber: 69\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                                    lineNumber: 1131,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            ]\n                                                        }, type.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                            lineNumber: 1127,\n                                                            columnNumber: 53\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1124,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"my-4 flex items-center w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        id: \"TrainingSummary\",\n                                                        placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                                                        className: \"!w-full  ring-1 ring-inset \",\n                                                        handleEditorChange: handleEditorChange,\n                                                        content: content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                                    lineNumber: 1211,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1001,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_18__.Separator, {\n                            className: \"my-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1233,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                    children: \"Signatures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1235,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full col-span-3 sm:col-span-2 grid grid-cols-1 phablet:grid-cols-2 gap-4\",\n                                    children: selectedMemberList && selectedMemberList.map((member, index)=>{\n                                        var _signatureMembers_find;\n                                        console.log(\"SignaturePad \".concat(index, \" - member data:\"), member);\n                                        console.log(\"SignaturePad \".concat(index, \" - member.label:\"), member.label);\n                                        console.log(\"SignaturePad \".concat(index, \" - member.value:\"), member.value);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            labe: true,\n                                            className: \"w-full\",\n                                            member: member.label,\n                                            memberId: member.value,\n                                            onSignatureChanged: (signature, member, memberId)=>onSignatureChanged(signature, member !== null && member !== void 0 ? member : \"\", memberId || 0),\n                                            signature: {\n                                                id: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.ID\n                                            }\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                            lineNumber: 1253,\n                                            columnNumber: 49\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1236,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1234,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 982,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 978,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_22__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                        size: \"sm\",\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/crew-training\"),\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1287,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                        size: \"sm\",\n                        onClick: handleSave,\n                        disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                        children: trainingID === 0 ? \"Create session\" : \"Update session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                        lineNumber: 1300,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1286,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[400px] sm:w-[540px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1313,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1312,\n                            columnNumber: 21\n                        }, undefined),\n                        training && trainingTypes.filter((type)=>{\n                            var _training_TrainingTypes;\n                            return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                        }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 px-2.5 sm:px-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1328,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: type.procedure\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                        lineNumber: 1330,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                lineNumber: 1325,\n                                columnNumber: 33\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1311,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1310,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialog, {\n                open: openCommentAlert,\n                onOpenChange: setOpenCommentAlert,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogTitle, {\n                                    children: \"Add comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1344,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogDescription, {\n                                    children: \"Add a comment for this procedure check.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1345,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1343,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_28__.Textarea, {\n                            value: currentComment,\n                            onChange: (e)=>setCurrentComment(e.target.value),\n                            placeholder: \"Enter your comment here...\",\n                            rows: 4\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1349,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1356,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_27__.AlertDialogAction, {\n                                    onClick: handleSaveComment,\n                                    children: \"Save comment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                                    lineNumber: 1357,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                            lineNumber: 1355,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                    lineNumber: 1342,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\create\\\\form.tsx\",\n                lineNumber: 1339,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TrainingForm, \"UdML0EXpA1BVLr9vIv8+ZAKBqfU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useMutation\n    ];\n});\n_c = TrainingForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingForm);\nvar _c;\n$RefreshReg$(_c, \"TrainingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/create/form.tsx\n"));

/***/ })

});