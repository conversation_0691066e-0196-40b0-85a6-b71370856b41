"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/logbook/forms/crew-training-event.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _crew_training_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../crew-training/type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/models/trainingType */ \"(app-pages-browser)/./src/app/offline/models/trainingType.js\");\n/* harmony import */ var _app_offline_models_memberTraining_Signature__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/offline/models/memberTraining_Signature */ \"(app-pages-browser)/./src/app/offline/models/memberTraining_Signature.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_trainingSessionDue__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/trainingSessionDue */ \"(app-pages-browser)/./src/app/offline/models/trainingSessionDue.js\");\n/* harmony import */ var _app_offline_models_trainingSession__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/trainingSession */ \"(app-pages-browser)/./src/app/offline/models/trainingSession.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingEvent = (param)=>{\n    let { trainingTypeId = 0, vesselId = 0, selectedEvent = false, currentTrip = false, closeModal, updateTripReport, tripReport, crewMembers, masterID, logBookConfig, vessels, locked, offline = false, logBookStartDate } = param;\n    var _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _rawTraining_trainingLocation2;\n    _s();\n    const [trainingID, setTrainingID] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(selectedEvent);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({});\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Date().toLocaleDateString());\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery)(\"(min-width: 640px)\");\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [vesselList, setVesselList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [openDescriptionPanel, setOpenDescriptionPanel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [descriptionPanelContent, setDescriptionPanelContent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [descriptionPanelHeading, setDescriptionPanelHeading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [currentFieldComment, setCurrentFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const [finishTime, setFinishTime] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const memberIdOptions = [\n        masterID,\n        ...Array.isArray(crewMembers) ? crewMembers.map((m)=>m.crewMemberID) : []\n    ];\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const trainingTypeModel = new _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_16__[\"default\"]();\n    const memberTraining_SignatureModel = new _app_offline_models_memberTraining_Signature__WEBPACK_IMPORTED_MODULE_17__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const trainingSessionDueModel = new _app_offline_models_trainingSessionDue__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const trainingSessionModel = new _app_offline_models_trainingSession__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypes)(setTrainingTypes);\n    }\n    const handleSetTraining = (t)=>{\n        const tDate = new Date(t.date).toLocaleDateString();\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(t.date).format(\"YYYY-MM-DD\"),\n            Members: t.members.nodes.map((m)=>m.id),\n            TrainerID: t.trainer.id,\n            trainingSummary: t.trainingSummary,\n            TrainingTypes: t.trainingTypes.nodes.map((t)=>t.id),\n            VesselID: vesselId,\n            FuelLevel: t.fuelLevel || 0,\n            GeoLocationID: t.geoLocationID,\n            StartTime: t.startTime,\n            FinishTime: t.finishTime,\n            Lat: t.lat,\n            Long: t.long\n        };\n        setContent(t.trainingSummary);\n        setStartTime(t.startTime);\n        setFinishTime(t.finishTime);\n        setRawTraining(t);\n        setTraining(trainingData);\n        if (+t.geoLocationID > 0) {\n            setCurrentLocation({\n                latitude: t.geoLocation.lat,\n                longitude: t.geoLocation.long\n            });\n        } else {\n            setCurrentLocation({\n                latitude: t.lat,\n                longitude: t.long\n            });\n        }\n        const members = t.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        setSelectedMemberList(members);\n        const signatures = t.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    // const handleSetVessels = (data: any) => {\n    //     const activeVessels = data?.filter((vessel: any) => !vessel.archived)\n    //     const formattedData = [\n    //         {\n    //             label: 'Other',\n    //             value: 'Other',\n    //         },\n    //         {\n    //             label: 'Desktop/shore',\n    //             value: 'Onshore',\n    //         },\n    //         ...activeVessels.map((vessel: any) => ({\n    //             value: vessel.id,\n    //             label: vessel.title,\n    //         })),\n    //     ]\n    //     setVessels(formattedData)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (vessels) {\n            const activeVessels = vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>!vessel.archived);\n            const formattedData = [\n                {\n                    label: \"Other\",\n                    value: \"Other\"\n                },\n                {\n                    label: \"Desktop/shore\",\n                    value: \"Onshore\"\n                },\n                ...activeVessels.map((vessel)=>({\n                        value: vessel.id,\n                        label: vessel.title\n                    }))\n            ];\n            setVesselList(formattedData);\n        }\n    }, [\n        vessels\n    ]);\n    // const [queryVessels] = useLazyQuery(VESSEL_LIST, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (queryVesselResponse: any) => {\n    //         if (queryVesselResponse.readVessels.nodes) {\n    //             handleSetVessels(queryVesselResponse.readVessels.nodes)\n    //         }\n    //     },\n    //     onError: (error: any) => {\n    //         console.error('queryVessels error', error)\n    //     },\n    // })\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                setTrainingID(data.id);\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id),\n                            eventCategory: \"CrewTraining\",\n                            crewTrainingID: data.id\n                        }\n                    }\n                });\n                closeModal();\n            } else {\n                console.error(\"mutationCreateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        if (offline) {\n            const allDues = await trainingSessionDueModel.getAll();\n            const data = allDues.filter((item)=>item.memberID === variables.filter.memberID.eq && item.vesselID === variables.filter.vesselID.eq && item.trainingTypeID === variables.filter.trainingTypeID.eq);\n            onCompleted(data);\n        } else {\n            const { data } = await readOneTrainingSessionDue({\n                variables: variables\n            });\n            onCompleted(data.readOneTrainingSessionDue);\n        }\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    if (offline) {\n                        // mutationCreateTrainingSessionDue\n                        await trainingSessionDueModel.save({\n                            ...item,\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)()\n                        });\n                    } else {\n                        await mutationCreateTrainingSessionDue(variables);\n                    }\n                } else {\n                    if (offline) {\n                        // mutationUpdateTrainingSessionDue\n                        await trainingSessionDueModel.save(item);\n                    } else {\n                        await mutationUpdateTrainingSessionDue(variables);\n                    }\n                }\n            }));\n        }\n    };\n    const [updateTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UpdateTripEvent, {\n        onCompleted: (response)=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating trip event\", error);\n        }\n    });\n    const [createTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CreateTripEvent, {\n        onCompleted: (response)=>{\n            const data = response.createTripEvent;\n            setCurrentEvent(data);\n            saveTraining();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip event\", error);\n        }\n    });\n    const saveTraining = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(logBookStartDate).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\",\n            fuelLevel: \"\".concat(training.FuelLevel),\n            geoLocationID: training.GeoLocationID,\n            startTime: startTime,\n            finishTime: finishTime,\n            lat: \"\".concat(training.Lat),\n            long: \"\".concat(training.Long)\n        };\n        if (trainingID === 0) {\n            if (offline) {\n                // mutationCreateTrainingSession\n                const data = await trainingSessionModel.save({\n                    ...input,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)()\n                });\n                setTrainingID(data.id);\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                // updateTripEvent\n                await tripEventModel.save({\n                    id: +(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id),\n                    eventCategory: \"CrewTraining\",\n                    crewTrainingID: data.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                await mutationCreateTrainingSession({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // mutationUpdateTrainingSession\n                const data = await trainingSessionModel.save(input);\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n            } else {\n                await mutationUpdateTrainingSession({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        // Validate Training Types - check if empty or undefined\n        if (!training.TrainingTypes || lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n            // Clear any previous error state for this field\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"Nature of training is required\"\n                }));\n        } else {\n            // Clear any previous error for this field when valid\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"\"\n                }));\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(logBookStartDate).format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            return;\n        }\n        if (currentEvent) {\n            if (offline) {\n                // updateTripEvent\n                await tripEventModel.save({\n                    id: +currentEvent.id,\n                    eventCategory: \"CrewTraining\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +currentEvent.id,\n                            eventCategory: \"CrewTraining\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n            saveTraining();\n            closeModal();\n        } else {\n            if (offline) {\n                // createTripEvent\n                const tripEventData = await tripEventModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)(),\n                    eventCategory: \"CrewTraining\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                setCurrentEvent(tripEventData);\n                saveTraining();\n            } else {\n                createTripEvent({\n                    variables: {\n                        input: {\n                            eventCategory: \"CrewTraining\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        if (offline) {\n            // queryGetMemberTrainingSignatures\n            const allSignatures = await memberTraining_SignatureModel.getAll();\n            const data = allSignatures.filter((item)=>item.memberID === signature.MemberID && item.trainingSessionID === TrainingID);\n            if (data.length > 0) {\n                // mutationUpdateMemberTrainingSignature\n                await memberTraining_SignatureModel.save({\n                    id: data[0].id,\n                    memberID: signature.MemberID,\n                    signatureData: signature.SignatureData,\n                    trainingSessionID: TrainingID\n                });\n            } else {\n                // mutationCreateMemberTrainingSignature\n                await memberTraining_SignatureModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)(),\n                    memberID: signature.MemberID,\n                    signatureData: signature.SignatureData,\n                    trainingSessionID: TrainingID\n                });\n            }\n        } else {\n            await queryGetMemberTrainingSignatures({\n                variables: {\n                    filter: {\n                        memberID: {\n                            eq: signature.MemberID\n                        },\n                        trainingSessionID: {\n                            in: TrainingID\n                        }\n                    }\n                }\n            }).then((response)=>{\n                const data = response.data.readMemberTraining_Signatures.nodes;\n                if (data.length > 0) {\n                    mutationUpdateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                id: data[0].id,\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                } else {\n                    if (signature.SignatureData) {\n                        mutationCreateMemberTrainingSignature({\n                            variables: {\n                                input: {\n                                    memberID: signature.MemberID,\n                                    signatureData: signature.SignatureData,\n                                    trainingSessionID: TrainingID\n                                }\n                            }\n                        });\n                    }\n                }\n            }).catch((error)=>{\n                console.error(\"mutationGetMemberTrainingSignatures error\", error);\n            });\n        }\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(new Date(date.toString()).toLocaleDateString());\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        console.log(\"handleTrainerChange (logbook) - received trainer:\", trainer);\n        if (!trainer) return; // Add early return if trainer is null\n        // With the updated CrewDropdown, trainer should now be a full object with label and value\n        // But we'll still handle the legacy case where it might just be an ID or array\n        const trainerValue = Array.isArray(trainer) ? trainer.length > 0 ? trainer[0].value || trainer[0] : null : trainer.value || trainer;\n        if (!trainerValue) {\n            return;\n        }\n        // Create a proper trainer object with the correct label format\n        const trainerObject = Array.isArray(trainer) ? trainer[0] : trainer.label ? trainer // Use the object as is if it has a label\n         : {\n            value: trainerValue,\n            label: trainer.profile ? \"\".concat(trainer.profile.firstName || \"\", \" \").concat(trainer.profile.surname || \"\") : \"Trainer \".concat(trainerValue)\n        };\n        console.log(\"handleTrainerChange (logbook) - created trainerObject:\", trainerObject);\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainerValue);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainerValue,\n            Members: members\n        });\n        // Add the trainer to the selectedMemberList if not already present\n        const trainerExists = selectedMemberList.some((member)=>member.value === trainerValue);\n        if (!trainerExists) {\n            console.log(\"Adding trainer to selectedMemberList (logbook):\", trainerObject);\n            setSelectedMemberList([\n                ...selectedMemberList,\n                trainerObject\n            ]);\n            setSignatureMembers([\n                ...signatureMembers,\n                {\n                    MemberID: +trainerValue,\n                    SignatureData: null\n                }\n            ]);\n        }\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        // Update training state with selected types\n        const selectedTypes = !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingTypes) ? trainingTypes.map((item)=>item.value) : [];\n        setTraining({\n            ...training,\n            TrainingTypes: selectedTypes\n        });\n        // Clear error message if valid selection is made\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(selectedTypes)) {\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"\"\n                }));\n        }\n    };\n    const handleMemberChange = (members)=>{\n        console.log(\"handleMemberChange (logbook) - received members:\", members);\n        // Ensure members is an array\n        const membersArray = Array.isArray(members) ? members : [\n            members\n        ].filter(Boolean);\n        // Make sure we're filtering with valid member values\n        const signatures = signatureMembers.filter((item)=>membersArray.some((m)=>m && m.value && +m.value === item.MemberID));\n        // Extract member values safely\n        const memberValues = membersArray.filter((item)=>item && item.value).map((item)=>item.value);\n        setTraining({\n            ...training,\n            Members: memberValues\n        });\n        console.log(\"handleMemberChange (logbook) - setting selectedMemberList to:\", membersArray);\n        setSelectedMemberList(membersArray);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (signature, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (signature) {\n            if (index !== -1) {\n                if (signature.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = signature;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: signature\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    }\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel.value\n        });\n    };\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            // getTripEvent\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                setTrainingID(event.crewTrainingID);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                setTrainingID(event.crewTraining.id);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                handleSetTraining(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    const loadTrainingSession = async ()=>{\n        if (offline) {\n            // queryTrainingSessionByID\n            const data = await trainingSessionModel.getById(trainingID);\n            if (data) {\n                handleSetTraining(data);\n            }\n        } else {\n            await queryTrainingSessionByID({\n                variables: {\n                    id: +trainingID\n                }\n            });\n        }\n    };\n    const handleStartTimeChange = (date)=>{\n        setStartTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const handleFinishTimeChange = (date)=>{\n        setFinishTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0 && ((_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTraining({\n                ...training,\n                GeoLocationID: +value.value,\n                Lat: null,\n                Long: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTraining({\n                ...training,\n                GeoLocationID: 0,\n                Lat: value.latitude,\n                Long: value.longitude\n            });\n        }\n    };\n    const handleSetCurrentLocation = (value)=>{\n        setTraining({\n            ...training,\n            GeoLocationID: 0,\n            Lat: value.latitude,\n            Long: value.longitude\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        selectedEvent,\n        currentEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (+trainingID > 0) {\n            loadTrainingSession();\n        }\n    }, [\n        trainingID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(training)) {\n            setTraining({\n                ...training,\n                VesselID: vesselId\n            });\n        }\n    }, [\n        training\n    ]);\n    const offlineUseEffect = async ()=>{\n        // getTrainingTypeByID(trainingTypeId, setTraining)\n        const training = await trainingTypeModel.getById(trainingTypeId);\n        setTraining(training);\n        // getTrainingTypes(setTrainingTypes)\n        const types = await trainingTypeModel.getAll();\n        setTrainingTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (offline) {\n            offlineUseEffect();\n        }\n    }, [\n        offline\n    ]);\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: type.customisedComponentField.nodes\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        var _rawTraining_procedureFields;\n        if (!trainingID) {\n            const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n            setBufferProcedureCheck([\n                ...procedureCheck,\n                {\n                    fieldId: field.id,\n                    status: status\n                }\n            ]);\n            return;\n        }\n        const nodes = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes;\n        const existingField = nodes === null || nodes === void 0 ? void 0 : nodes.find((procedureField)=>procedureField.customisedComponentFieldID === field.id);\n        if (!nodes || !existingField) {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n            return;\n        }\n        if (nodes.length > 0 && existingField) {\n            const fieldID = existingField.id;\n            updateCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        id: +fieldID,\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n        } else {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n        }\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setCurrentFieldComment(fieldComment);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        if (!trainingID) {\n            const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n            setBufferFieldComment([\n                ...fieldComment,\n                {\n                    fieldId: currentField.id,\n                    comment: currentComment\n                }\n            ]);\n            setOpenCommentAlert(false);\n            return;\n        }\n        if (currentFieldComment) {\n            var _rawTraining_procedureFields;\n            updateCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        id: currentFieldComment.id,\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: currentField.id,\n                        comment: currentComment\n                    }\n                }\n            });\n            const nodes = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes;\n            if (nodes) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== currentField.id),\n                            {\n                                ...currentFieldComment,\n                                comment: currentComment\n                            }\n                        ]\n                    }\n                });\n            }\n        } else {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: currentField.id,\n                        comment: currentComment\n                    }\n                }\n            });\n        }\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1340,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Name of trainer\",\n                        disabled: locked,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                disabled: locked,\n                                offline: offline,\n                                value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                onChange: handleTrainerChange,\n                                memberIdOptions: memberIdOptions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1344,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                className: \"text-destructive\",\n                                children: hasFormErrors && formErrors.TrainerID\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1351,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1343,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Crew trained\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            value: training === null || training === void 0 ? void 0 : training.Members,\n                            onChange: handleMemberChange,\n                            memberIdOptions: memberIdOptions\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1356,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1355,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Training types\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-6 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    offline: offline,\n                                    value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                    onChange: handleTrainingTypeChange,\n                                    locked: locked\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1366,\n                                    columnNumber: 29\n                                }, undefined),\n                                formErrors.TrainingTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"text-destructive mt-1\",\n                                    children: formErrors.TrainingTypes\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1373,\n                                    columnNumber: 33\n                                }, undefined),\n                                training && trainingTypes.filter((type)=>{\n                                    var _training_TrainingTypes;\n                                    return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                    variant: \"primary\",\n                                    className: \"w-fit flex-1\",\n                                    onClick: ()=>setOpenViewProcedure(true),\n                                    children: \"View Procedures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1385,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1365,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1364,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Location of training\",\n                        disabled: locked,\n                        children: vesselList && (rawTraining || trainingID === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                            options: vesselList || [],\n                            value: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? vesselList === null || vesselList === void 0 ? void 0 : vesselList.filter((vessel)=>+vessel.value === +(training === null || training === void 0 ? void 0 : training.VesselID))[0] : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? {\n                                label: \"Desktop/shore\",\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType\n                            } : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? {\n                                label: \"Other\",\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType\n                            } : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? {\n                                label: rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.title,\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation2 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation2 === void 0 ? void 0 : _rawTraining_trainingLocation2.id\n                            } : (vesselList === null || vesselList === void 0 ? void 0 : vesselList.find((vessel)=>+vessel.value === +vesselId)) || null,\n                            onChange: handleTrainingVesselChange,\n                            placeholder: \"Select location\",\n                            isDisabled: true,\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1398,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1396,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        className: \"text-destructive\",\n                        children: hasFormErrors && formErrors.VesselID\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1444,\n                        columnNumber: 21\n                    }, undefined),\n                    displayField(\"CrewTraining_FuelLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Fuel level at end of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_22__.Input, {\n                            id: \"fuel-level\",\n                            name: \"fuel-level\",\n                            type: \"number\",\n                            defaultValue: training === null || training === void 0 ? void 0 : training.FuelLevel,\n                            className: \"w-full\",\n                            placeholder: \"Fuel level\",\n                            onChange: lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()(function(e) {\n                                setTraining({\n                                    ...training,\n                                    FuelLevel: e.target.value\n                                });\n                            }, 600)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1452,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1449,\n                        columnNumber: 25\n                    }, undefined),\n                    displayField(\"CrewTraining_StartTime\") || displayField(\"CrewTraining_FinishTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Training duration\",\n                        className: \"mb-1\",\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1470,\n                        columnNumber: 29\n                    }, undefined),\n                    displayField(\"CrewTraining_StartTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Start time of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            time: startTime,\n                            handleTimeChange: handleStartTimeChange,\n                            timeID: \"startTime\",\n                            fieldName: \"Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1479,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1478,\n                        columnNumber: 25\n                    }, undefined),\n                    displayField(\"CrewTraining_FinishTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"End time of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            time: finishTime,\n                            handleTimeChange: handleFinishTimeChange,\n                            timeID: \"finishTime\",\n                            fieldName: \"Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1490,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1489,\n                        columnNumber: 25\n                    }, undefined),\n                    (!currentEvent || training) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        id: \"TrainingSummary\",\n                        placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                        handleEditorChange: handleEditorChange,\n                        content: content,\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1500,\n                        columnNumber: 25\n                    }, undefined),\n                    selectedMemberList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                children: \"Participant Signatures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1519,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                children: selectedMemberList.map((member, index)=>{\n                                    var _signatureMembers_find, _signatureMembers_find1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        title: member.label,\n                                        member: member.label,\n                                        memberId: member.value,\n                                        onSignatureChanged: onSignatureChanged,\n                                        signature: {\n                                            signatureData: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.SignatureData,\n                                            id: (_signatureMembers_find1 = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find1 === void 0 ? void 0 : _signatureMembers_find1.ID\n                                        },\n                                        locked: locked\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                        lineNumber: 1523,\n                                        columnNumber: 41\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1520,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end pb-4 pt-4 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                variant: \"back\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                                onClick: closeModal,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1552,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                variant: \"primary\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                                onClick: locked ? ()=>{} : handleSave,\n                                disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                                children: trainingID === 0 ? \"Save\" : \"Update\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1558,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1551,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1342,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-3/4 sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1575,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1574,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetBody, {\n                            children: training && trainingTypes.filter((type)=>{\n                                var _training_TrainingTypes;\n                                return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                            }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-md p-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium leading-6 mb-4\",\n                                            children: type.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                            lineNumber: 1590,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            dangerouslySetInnerHTML: {\n                                                __html: type.procedure\n                                            }\n                                        }, type.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                            lineNumber: 1593,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, type.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1587,\n                                    columnNumber: 37\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1577,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                    lineNumber: 1573,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1572,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n        lineNumber: 1338,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingEvent, \"IFIVVo0IGCE+7GWuQeWlOiL77ts=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation\n    ];\n});\n_c = CrewTrainingEvent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingEvent);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingEvent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\n"));

/***/ })

});