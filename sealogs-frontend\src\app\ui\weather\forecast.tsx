import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import <PERSON><PERSON>ield from '../logbook/components/location'
import { Label } from '@/components/ui/label'
import { debounce } from 'lodash'
import {
    getPrecipitation,
    getSwellHeightRange,
    getVisibility,
    getWindDirection,
} from '@/app/helpers/weatherHelper'
import {
    CreateWeatherForecast,
    DeleteWeatherForecasts,
    UpdateWeatherForecast,
} from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'
import WeatherForecastList from './forecast-list'
import { useOnline } from '@reactuses/core'
import WeatherForecastModel from '@/app/offline/models/weatherForecast'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { formatDate } from '@/app/helpers/dateHelper'
import { GET_GEO_LOCATIONS } from '@/app/lib/graphQL/query'
import { Button } from '@/components/ui/button'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { Check, X, Trash, Plus, Clock } from 'lucide-react'
import { H2, P } from '@/components/ui/typography'
import { Card } from '@/components/ui'
import DatePicker from '@/components/DateRange'
import WindWidget from './widgets/wind-widget'
import SwellWidget from './widgets/swell-widget'
import CloudWidget from './widgets/cloud-widget'
import BarometerWidget from './widgets/barometer-widget'

const WeatherForecast = ({
    logBookEntryID,
    offline = false,
    locked, // Used in child components
}: {
    logBookEntryID: number
    offline?: boolean
    locked: boolean
}) => {
    const [isWriteModeForecast, setIsWriteModeForecast] = useState(false)
    const [isManualEntry, setIsManualEntry] = useState(false)
    const [forecast, setForecast] = useState({} as any)
    // Current location state for the location field
    const [currentLocation] = useState<any>({
        latitude: null,
        longitude: null,
    })
    const [selectedCoordinates, setSelectedCoordinates] = useState<any>({
        latitude: null,
        longitude: null,
    })
    const isOnline = useOnline()
    const forecastModel = new WeatherForecastModel()
    const [isStormGlassLoading, setIsStormGlassLoading] = useState(false)
    const [refreshList, setRefreshList] = useState(false)
    const [geoLocations, setGeoLocations] = useState([])
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

    const getTimeNow = () => {
        return dayjs().format('HH:mm')
    }

    const [getGeoLocations] = useLazyQuery(GET_GEO_LOCATIONS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readGeoLocations.nodes
            if (data) {
                setGeoLocations(data)
            }
        },
        onError: (error: any) => {
            console.error('queryGeoLocations error', error)
        },
    })

    const getDayNow = () => {
        return dayjs().format('YYYY-MM-DD')
    }

    const initForecast = () => {
        setForecast({
            id: 0,
            time: getTimeNow(),
            day: getDayNow(),
            geoLocationID: 0,
            lat: 0,
            long: 0,
            logBookEntryID: logBookEntryID,
        })
    }
    const createForecast = () => {
        initForecast()
        setIsWriteModeForecast(true)
    }

    const handleSetCurrentLocation = (value: any) => {
        setForecast({
            ...forecast,
            geoLocationID: 0,
            lat: value.latitude,
            long: value.longitude,
        })
        setSelectedCoordinates({
            latitude: value.latitude,
            longitude: value.longitude,
        })
    }
    const handleLocationChange = (value: any) => {
        // If value is null or undefined, return early
        if (!value) return

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setForecast({
                ...forecast,
                geoLocationID: +value.value,
                lat: null,
                long: null,
            })

            // If the value object has latitude and longitude, use them directly
            if (value.latitude !== undefined && value.longitude !== undefined) {
                setSelectedCoordinates({
                    latitude: value.latitude,
                    longitude: value.longitude,
                })
            } else {
                // Otherwise find the location in geoLocations by ID
                geoLocations.find((item: any) => {
                    if (item.id == +value.value) {
                        setSelectedCoordinates({
                            latitude: item.lat,
                            longitude: item.long,
                        })
                        return true
                    }
                })
            }
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setForecast({
                ...forecast,
                geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })

            // Update selected coordinates
            setSelectedCoordinates({
                latitude: value.latitude,
                longitude: value.longitude,
            })
        }
    }

    const processStormGlassData = (data: any) => {
        const {
            windSpeed,
            windDirection,
            swellHeight,
            visibility,
            precipitation,
            pressure,
            cloudCover,
        } = data.hours[0]

        const windSpeedInKnots = (
            windSpeed ? (windSpeed.noaa || windSpeed.sg || 0) / 0.51444 : 0
        ).toFixed(0) // Convert m/s to knot. One knot is equal to approximately 0.51444 meters per second (m/s).

        const compassWindDirection = getWindDirection(
            windDirection ? windDirection.noaa || windDirection.sg || 0 : 0,
        ) // convert degrees to compass direction

        const swellValue = getSwellHeightRange(
            swellHeight ? swellHeight.noaa || swellHeight.sg || 0 : 0,
        )

        const visibilityValue = getVisibility(
            visibility ? visibility.noaa || visibility.sg || 0 : 0,
        )

        const precipitationValue = getPrecipitation(
            precipitation ? precipitation.noaa || precipitation.sg || 0 : 0,
        )

        const pressureValue = pressure ? pressure.noaa || pressure.sg || 0 : 0

        const cloudCoverValue = (
            cloudCover ? cloudCover.noaa || cloudCover.sg || 0 : 0
        ).toFixed(0)

        setForecast({
            ...forecast,
            windSpeed: +windSpeedInKnots,
            windDirection: compassWindDirection,
            swell: swellValue,
            visibility: visibilityValue,
            precipitation: precipitationValue,
            pressure: +pressureValue,
            cloudCover: +cloudCoverValue,
        })
        setIsStormGlassLoading(false)
    }
    const isStormGlassButtonEnabled = () => {
        let isStormGlassButtonEnabled = false
        if (+forecast.geoLocationID > 0) {
            isStormGlassButtonEnabled = true
        } else if (!isNaN(+forecast.lat) || !isNaN(+forecast.long)) {
            isStormGlassButtonEnabled = true
        }
        if (!isOnline) {
            isStormGlassButtonEnabled = false
        }
        return isStormGlassButtonEnabled
    }
    const getStormGlassData = () => {
        setIsManualEntry(false)
        if (forecast.geoLocationID > 0) {
            toast.loading('Retrieving forecast...')
            setIsStormGlassLoading(true)
            const dateString = `${forecast.day} ${forecast.time}`
            let startDate = new Date(dateString)
            let endDate = startDate
            var headers = {
                'Cache-Control': 'no-cache',
                Authorization: process.env.STORMGLASS_API_KEY || '',
                'Access-Control-Allow-Credentials': 'true',
            }
            var params =
                'windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover'
            const url = `https://api.stormglass.io/v2/weather/point?lat=${selectedCoordinates.latitude || 0}&lng=${
                selectedCoordinates.longitude || 0
            }&params=${params}&start=${startDate.toISOString()}&end=${endDate.toISOString()}`
            let request = fetch(url, {
                method: 'GET',
                headers,
            })
            request
                .then((response) => response.json())
                .then((jsonData) => {
                    toast.dismiss()
                    toast.success('Forecast retrieved successfully')
                    processStormGlassData(jsonData)
                })
                .catch((error) => {
                    setIsStormGlassLoading(false)
                    toast.dismiss()
                    toast.error(
                        'There was a problem retrieving the forecast. Please try again later.',
                    )
                    console.error('Catch error:', error)
                })
            return request
        } else {
            if ('geolocation' in navigator) {
                toast.loading('Retrieving forecast...')
                setIsStormGlassLoading(true)
                return new Promise((resolve, reject) => {
                    return navigator.geolocation.getCurrentPosition(
                        () => {
                            const dateString = `${forecast.day} ${forecast.time}`
                            let startDate = new Date(dateString)
                            let endDate = startDate
                            var headers = {
                                'Cache-Control': 'no-cache',
                                Authorization:
                                    process.env.STORMGLASS_API_KEY || '',
                                'Access-Control-Allow-Credentials': 'true',
                            }
                            var params =
                                'windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover'
                            const url = `https://api.stormglass.io/v2/weather/point?lat=${selectedCoordinates.latitude || 0}&lng=${
                                selectedCoordinates.longitude || 0
                            }&params=${params}&start=${startDate.toISOString()}&end=${endDate.toISOString()}`
                            let request = fetch(url, {
                                method: 'GET',
                                headers,
                            })
                            request
                                .then((response) => response.json())
                                .then((jsonData) => {
                                    toast.dismiss()
                                    toast.success(
                                        'Forecast retrieved successfully',
                                    )
                                    processStormGlassData(jsonData)
                                    resolve(jsonData)
                                })
                                .catch((error) => {
                                    setIsStormGlassLoading(false)
                                    reject(error)
                                    toast.dismiss()
                                    toast.error(
                                        'There was a problem retrieving the forecast. Please try again later.',
                                    )
                                    console.error('Catch error:', error)
                                })

                            return request
                        },
                        (error) => {
                            setIsStormGlassLoading(false)
                            reject(error)
                            toast.error(
                                'There was a problem retrieving the forecast. Please try again later.',
                            )
                            console.error('Geolocation error', error)
                        },
                    )
                })
            } else {
                setIsStormGlassLoading(false)
                console.error('Geolocation is not supported by your browser')
                toast.error('Geolocation is not supported by your browser')
            }
        }
    }

    const handleOnChangePressure = (value: any) => {
        const pressure = Array.isArray(value) ? value[0] : value
        setForecast({
            ...forecast,
            pressure: pressure,
        })
    }

    const handleOnChangeSeaSwell = (item: any) => {
        if (item) {
            setForecast({
                ...forecast,
                swell: item.value,
            })
        }
    }

    const handleWindChange = (values: {
        speed: number
        direction: {
            label: string
            degrees: number
            value: string
        }
    }) => {
        // Update both wind speed and direction in the forecast state
        setForecast({
            ...forecast,
            windSpeed: values.speed,
            windDirection: values.direction.value,
        })
    }

    const handleSetComment = debounce((item: any) => {
        setForecast({
            ...forecast,
            comment: item,
        })
    }, 600)
    const [createWeatherForecast, { loading: createWeatherForecastLoading }] =
        useMutation(CreateWeatherForecast, {
            onCompleted: () => {
                setIsWriteModeForecast(false)
                setRefreshList(true)
            },
            onError: (error) => {
                console.error('CreateWeatherForecast Error', error)
            },
        })
    const [updateWeatherForecast, { loading: updateWeatherForecastLoading }] =
        useMutation(UpdateWeatherForecast, {
            onCompleted: () => {
                setIsWriteModeForecast(false)
                setRefreshList(true)
            },
            onError: (error) => {
                console.error('UpdateWeatherForecast Error', error)
            },
        })
    const handleSave = async () => {
        if (+forecast.id === 0) {
            if (offline) {
                await forecastModel.save({
                    ...forecast,
                    id: generateUniqueId(),
                })
                setIsWriteModeForecast(false)
                setRefreshList(true)
            } else {
                await createWeatherForecast({
                    variables: {
                        input: {
                            ...forecast,
                        },
                    },
                })
            }
        } else {
            if (forecast.geoLocation) delete forecast.geoLocation
            if (forecast.__typename) delete forecast.__typename
            if (offline) {
                await forecastModel.save({
                    ...forecast,
                    day: dayjs(forecast.day).format('YYYY-MM-DD'),
                })
                setIsWriteModeForecast(false)
                setRefreshList(true)
            } else {
                await updateWeatherForecast({
                    variables: {
                        input: {
                            ...forecast,
                        },
                    },
                })
            }
        }
    }
    const handleCancel = () => {
        initForecast()
        setIsWriteModeForecast(false)
    }
    const handleForecastClick = (forecast: any) => {
        if (locked) {
            return
        }

        setIsManualEntry(false)
        const newForecast = {
            ...forecast,
            time: formatTime(forecast.time),
            day: dayjs(forecast.day.toString()).format('YYYY-MM-DD'),
        }
        setForecast(newForecast)
        setIsWriteModeForecast(true)
    }
    const [deleteWeatherForecasts, { loading: deleteWeatherForecastsLoading }] =
        useMutation(DeleteWeatherForecasts, {
            onCompleted: () => {
                setIsWriteModeForecast(false)
                setRefreshList(true)
            },
            onError: (error) => {
                console.error('DeleteWeatherForecasts Error', error)
            },
        })
    const handleDeleteForecast = async () => {
        if (offline) {
            forecastModel.delete(forecast)
            setIsWriteModeForecast(false)
            setRefreshList(true)
        } else {
            await deleteWeatherForecasts({
                variables: {
                    ids: [forecast.id],
                },
            })
        }
    }
    const formatTime = (time: string) => {
        return dayjs(`${dayjs().format('YYYY-MM-DD')} ${time}`).format('HH:mm')
    }

    const handleOnChangeCloudWidget = (item: any) => {
        setForecast({
            ...forecast,
            visibility: item.visibility.value,
            precipitation: item.precipitation.value,
            cloudCover: item.cloudCover,
        })
    }

    // Format time string for display
    useEffect(() => {
        if (logBookEntryID > 0) {
            setForecast({
                ...forecast,
                logBookEntryID: logBookEntryID,
            })
        }
        getGeoLocations()
    }, [logBookEntryID])

    return (
        <Card>
            {!isWriteModeForecast && (
                <>
                    <section className="mb-4">
                        <H2>Weather forecast</H2>
                        <P>
                            You can start by retrieving a weather forecast up to
                            7-days into the future. SeaLogs currently using the
                            Stormglass API with more forecasting services coming
                            soon.
                        </P>
                        <P>
                            After retrieving a forecast you can add your own
                            observations. We use this data to compare the
                            accuracy of forecasts plus share weather
                            observations with our community of users.
                        </P>
                    </section>
                    <div className="flex-1 flex justify-end">
                        <Button
                            disabled={locked}
                            onClick={() => createForecast()}>
                            Add another forecast
                        </Button>
                    </div>
                </>
            )}
            {isWriteModeForecast && (
                <section>
                    <article>
                        {/* Responsive container for DatePicker and LocationField */}
                        <div className="flex flex-col md:flex-row gap-4 mb-4">
                            {/* DatePicker - full width on mobile, half width on desktop */}
                            <div className="w-full md:w-1/2 relative">
                                <DatePicker
                                    id="forecast-date"
                                    name="forecast-date"
                                    label="Date and time of forecast required"
                                    value={
                                        forecast.day && forecast.time
                                            ? new Date(
                                                  `${forecast.day}T${forecast.time}`,
                                              )
                                            : undefined
                                    }
                                    mode="single"
                                    type="datetime" // Keep datetime to include time picker
                                    onChange={(date: any) => {
                                        if (date) {
                                            const newDate = dayjs(date)
                                            setForecast({
                                                ...forecast,
                                                day: newDate.format(
                                                    'YYYY-MM-DD',
                                                ),
                                                time: newDate.format(
                                                    'HH:mm:00',
                                                ),
                                            })
                                        }
                                    }}
                                    dateFormat="dd MMM,"
                                    timeFormat="HH:mm" // Explicitly set time format
                                    placeholder="Time"
                                    closeOnSelect={false} // Allow time selection
                                    clearable={true}
                                    icon={Clock}
                                    className="w-full"
                                    includeTime={true}
                                />
                            </div>

                            {/* LocationField - full width on mobile, half width on desktop */}
                            <div className="w-full md:w-1/2 flex flex-col">
                                <Label
                                    label="Location for forecast"
                                    className="w-full">
                                    <div className="w-full">
                                        <LocationField
                                            offline={offline}
                                            setCurrentLocation={
                                                handleSetCurrentLocation
                                            }
                                            handleLocationChange={(e: any) => {
                                                handleLocationChange(e)
                                            }}
                                            currentEvent={{
                                                geoLocationID:
                                                    forecast.geoLocationID,
                                                lat: forecast.lat,
                                                long: forecast.long,
                                            }}
                                        />
                                    </div>
                                </Label>
                            </div>
                        </div>

                        {/* Responsive container for buttons */}
                        <div className="flex flex-col md:flex-row gap-4 mb-4">
                            {isWriteModeForecast &&
                                isStormGlassButtonEnabled() && (
                                    <div className="w-full md:w-1/2">
                                        <Button
                                            onClick={() => getStormGlassData()}
                                            disabled={
                                                isStormGlassLoading ||
                                                createWeatherForecastLoading ||
                                                updateWeatherForecastLoading ||
                                                deleteWeatherForecastsLoading
                                            }
                                            className="w-full">
                                            {isStormGlassLoading
                                                ? 'Retrieving forecast...'
                                                : 'Retrieve forecast (API)'}
                                        </Button>
                                    </div>
                                )}
                            {isWriteModeForecast && (
                                <div className="w-full md:w-1/2">
                                    <Button
                                        variant="secondary"
                                        onClick={() => setIsManualEntry(true)}
                                        className="w-full">
                                        OR enter a manual forecast (observation)
                                    </Button>
                                </div>
                            )}
                        </div>
                        <div
                            className={
                                isManualEntry
                                    ? 'flex flex-col gap-4'
                                    : 'grid grid-cols-2 gap-2 sm:grid-cols-4 sm:gap-4'
                            }>
                            <WindWidget
                                editMode={isManualEntry}
                                speed={forecast.windSpeed}
                                direction={forecast.windDirection}
                                onChange={handleWindChange}
                            />
                            <CloudWidget
                                editMode={isManualEntry}
                                visibilityValue={forecast.visibility}
                                precipitationValue={forecast.precipitation}
                                cloudCoverValue={forecast.cloudCover}
                                onChange={handleOnChangeCloudWidget}
                            />
                            <SwellWidget
                                editMode={isManualEntry}
                                value={forecast.swell}
                                onChange={handleOnChangeSeaSwell}
                            />
                            <BarometerWidget
                                value={forecast.pressure}
                                editMode={isManualEntry}
                                onChange={handleOnChangePressure}
                            />
                        </div>
                        {!isManualEntry && (
                            <div className="text-center text-[10px]">
                                Forecast provided by Stormglass API
                            </div>
                        )}
                        <div className="mt-4">
                            <Label label="Your comments">
                                <Textarea
                                    id={`forecast-comment`}
                                    rows={4}
                                    placeholder="Comments ..."
                                    defaultValue={forecast.comment || ''}
                                    onChange={(e: any) =>
                                        handleSetComment(e.target.value)
                                    }
                                />
                            </Label>
                        </div>
                    </article>

                    <footer className="flex flex-col-reverse small:flex-row gap-2.5 small:justify-end mt-6">
                        <div className="flex gap-2">
                            <Button
                                variant="back"
                                className="w-full small:w-fit"
                                onClick={handleCancel}
                                disabled={
                                    isStormGlassLoading ||
                                    createWeatherForecastLoading ||
                                    updateWeatherForecastLoading ||
                                    deleteWeatherForecastsLoading
                                }>
                                Cancel
                            </Button>
                            {+forecast.id > 0 && (
                                <Button
                                    variant="destructive"
                                    className="w-full small:w-fit"
                                    iconLeft={Trash}
                                    onClick={() => {
                                        setDeleteDialogOpen(true)
                                    }}
                                    disabled={
                                        isStormGlassLoading ||
                                        createWeatherForecastLoading ||
                                        updateWeatherForecastLoading ||
                                        deleteWeatherForecastsLoading
                                    }>
                                    Delete
                                </Button>
                            )}
                        </div>
                        <Button
                            iconLeft={Check}
                            onClick={handleSave}
                            disabled={
                                isStormGlassLoading ||
                                createWeatherForecastLoading ||
                                updateWeatherForecastLoading ||
                                deleteWeatherForecastsLoading
                            }>
                            {`${+forecast.id === 0 ? 'Save' : 'Update'} forecast`}
                        </Button>
                    </footer>
                </section>
            )}
            {!isWriteModeForecast && (
                <section>
                    <WeatherForecastList
                        offline={offline}
                        logBookEntryID={logBookEntryID}
                        refreshList={refreshList}
                        onClick={handleForecastClick}
                    />
                </section>
            )}

            <AlertDialogNew
                openDialog={deleteDialogOpen}
                setOpenDialog={setDeleteDialogOpen}
                handleCreate={handleDeleteForecast}
                title="Delete Forecast Data"
                description={`Are you sure you want to delete the forecast data for ${forecast.time ? formatTime(forecast.time) : ''} / ${forecast.day ? formatDate(forecast.day) : ''}?`}
                variant="danger"
                actionText="Delete"
                cancelText="Cancel"
            />
        </Card>
    )
}

export default WeatherForecast
