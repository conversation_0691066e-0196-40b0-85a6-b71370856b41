"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/logbook/forms/crew-training-event.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _crew_training_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../crew-training/type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/models/trainingType */ \"(app-pages-browser)/./src/app/offline/models/trainingType.js\");\n/* harmony import */ var _app_offline_models_memberTraining_Signature__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/offline/models/memberTraining_Signature */ \"(app-pages-browser)/./src/app/offline/models/memberTraining_Signature.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_trainingSessionDue__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/trainingSessionDue */ \"(app-pages-browser)/./src/app/offline/models/trainingSessionDue.js\");\n/* harmony import */ var _app_offline_models_trainingSession__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/trainingSession */ \"(app-pages-browser)/./src/app/offline/models/trainingSession.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingEvent = (param)=>{\n    let { trainingTypeId = 0, vesselId = 0, selectedEvent = false, currentTrip = false, closeModal, updateTripReport, tripReport, crewMembers, masterID, logBookConfig, vessels, locked, offline = false, logBookStartDate } = param;\n    var _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _rawTraining_trainingLocation2;\n    _s();\n    const [trainingID, setTrainingID] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(selectedEvent);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({});\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Date().toLocaleDateString());\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery)(\"(min-width: 640px)\");\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [vesselList, setVesselList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [openDescriptionPanel, setOpenDescriptionPanel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [descriptionPanelContent, setDescriptionPanelContent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [descriptionPanelHeading, setDescriptionPanelHeading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [currentFieldComment, setCurrentFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const [finishTime, setFinishTime] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const memberIdOptions = [\n        masterID,\n        ...Array.isArray(crewMembers) ? crewMembers.map((m)=>m.crewMemberID) : []\n    ];\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const trainingTypeModel = new _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_16__[\"default\"]();\n    const memberTraining_SignatureModel = new _app_offline_models_memberTraining_Signature__WEBPACK_IMPORTED_MODULE_17__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const trainingSessionDueModel = new _app_offline_models_trainingSessionDue__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const trainingSessionModel = new _app_offline_models_trainingSession__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypes)(setTrainingTypes);\n    }\n    const handleSetTraining = (t)=>{\n        const tDate = new Date(t.date).toLocaleDateString();\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(t.date).format(\"YYYY-MM-DD\"),\n            Members: t.members.nodes.map((m)=>m.id),\n            TrainerID: t.trainer.id,\n            trainingSummary: t.trainingSummary,\n            TrainingTypes: t.trainingTypes.nodes.map((t)=>t.id),\n            VesselID: vesselId,\n            FuelLevel: t.fuelLevel || 0,\n            GeoLocationID: t.geoLocationID,\n            StartTime: t.startTime,\n            FinishTime: t.finishTime,\n            Lat: t.lat,\n            Long: t.long\n        };\n        setContent(t.trainingSummary);\n        setStartTime(t.startTime);\n        setFinishTime(t.finishTime);\n        setRawTraining(t);\n        setTraining(trainingData);\n        if (+t.geoLocationID > 0) {\n            setCurrentLocation({\n                latitude: t.geoLocation.lat,\n                longitude: t.geoLocation.long\n            });\n        } else {\n            setCurrentLocation({\n                latitude: t.lat,\n                longitude: t.long\n            });\n        }\n        const members = t.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        setSelectedMemberList(members);\n        const signatures = t.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    // const handleSetVessels = (data: any) => {\n    //     const activeVessels = data?.filter((vessel: any) => !vessel.archived)\n    //     const formattedData = [\n    //         {\n    //             label: 'Other',\n    //             value: 'Other',\n    //         },\n    //         {\n    //             label: 'Desktop/shore',\n    //             value: 'Onshore',\n    //         },\n    //         ...activeVessels.map((vessel: any) => ({\n    //             value: vessel.id,\n    //             label: vessel.title,\n    //         })),\n    //     ]\n    //     setVessels(formattedData)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (vessels) {\n            const activeVessels = vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>!vessel.archived);\n            const formattedData = [\n                {\n                    label: \"Other\",\n                    value: \"Other\"\n                },\n                {\n                    label: \"Desktop/shore\",\n                    value: \"Onshore\"\n                },\n                ...activeVessels.map((vessel)=>({\n                        value: vessel.id,\n                        label: vessel.title\n                    }))\n            ];\n            setVesselList(formattedData);\n        }\n    }, [\n        vessels\n    ]);\n    // const [queryVessels] = useLazyQuery(VESSEL_LIST, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (queryVesselResponse: any) => {\n    //         if (queryVesselResponse.readVessels.nodes) {\n    //             handleSetVessels(queryVesselResponse.readVessels.nodes)\n    //         }\n    //     },\n    //     onError: (error: any) => {\n    //         console.error('queryVessels error', error)\n    //     },\n    // })\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                setTrainingID(data.id);\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id),\n                            eventCategory: \"CrewTraining\",\n                            crewTrainingID: data.id\n                        }\n                    }\n                });\n                closeModal();\n            } else {\n                console.error(\"mutationCreateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        if (offline) {\n            const allDues = await trainingSessionDueModel.getAll();\n            const data = allDues.filter((item)=>item.memberID === variables.filter.memberID.eq && item.vesselID === variables.filter.vesselID.eq && item.trainingTypeID === variables.filter.trainingTypeID.eq);\n            onCompleted(data);\n        } else {\n            const { data } = await readOneTrainingSessionDue({\n                variables: variables\n            });\n            onCompleted(data.readOneTrainingSessionDue);\n        }\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    if (offline) {\n                        // mutationCreateTrainingSessionDue\n                        await trainingSessionDueModel.save({\n                            ...item,\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)()\n                        });\n                    } else {\n                        await mutationCreateTrainingSessionDue(variables);\n                    }\n                } else {\n                    if (offline) {\n                        // mutationUpdateTrainingSessionDue\n                        await trainingSessionDueModel.save(item);\n                    } else {\n                        await mutationUpdateTrainingSessionDue(variables);\n                    }\n                }\n            }));\n        }\n    };\n    const [updateTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UpdateTripEvent, {\n        onCompleted: (response)=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating trip event\", error);\n        }\n    });\n    const [createTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CreateTripEvent, {\n        onCompleted: (response)=>{\n            const data = response.createTripEvent;\n            setCurrentEvent(data);\n            saveTraining();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip event\", error);\n        }\n    });\n    const saveTraining = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(logBookStartDate).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\",\n            fuelLevel: \"\".concat(training.FuelLevel),\n            geoLocationID: training.GeoLocationID,\n            startTime: startTime,\n            finishTime: finishTime,\n            lat: \"\".concat(training.Lat),\n            long: \"\".concat(training.Long)\n        };\n        if (trainingID === 0) {\n            if (offline) {\n                // mutationCreateTrainingSession\n                const data = await trainingSessionModel.save({\n                    ...input,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)()\n                });\n                setTrainingID(data.id);\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                // updateTripEvent\n                await tripEventModel.save({\n                    id: +(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id),\n                    eventCategory: \"CrewTraining\",\n                    crewTrainingID: data.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                await mutationCreateTrainingSession({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // mutationUpdateTrainingSession\n                const data = await trainingSessionModel.save(input);\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n            } else {\n                await mutationUpdateTrainingSession({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        // Validate Training Types - check if empty or undefined\n        if (!training.TrainingTypes || lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n            // Clear any previous error state for this field\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"Nature of training is required\"\n                }));\n        } else {\n            // Clear any previous error for this field when valid\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"\"\n                }));\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(logBookStartDate).format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            return;\n        }\n        if (currentEvent) {\n            if (offline) {\n                // updateTripEvent\n                await tripEventModel.save({\n                    id: +currentEvent.id,\n                    eventCategory: \"CrewTraining\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +currentEvent.id,\n                            eventCategory: \"CrewTraining\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n            saveTraining();\n            closeModal();\n        } else {\n            if (offline) {\n                // createTripEvent\n                const tripEventData = await tripEventModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)(),\n                    eventCategory: \"CrewTraining\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                setCurrentEvent(tripEventData);\n                saveTraining();\n            } else {\n                createTripEvent({\n                    variables: {\n                        input: {\n                            eventCategory: \"CrewTraining\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        if (offline) {\n            // queryGetMemberTrainingSignatures\n            const allSignatures = await memberTraining_SignatureModel.getAll();\n            const data = allSignatures.filter((item)=>item.memberID === signature.MemberID && item.trainingSessionID === TrainingID);\n            if (data.length > 0) {\n                // mutationUpdateMemberTrainingSignature\n                await memberTraining_SignatureModel.save({\n                    id: data[0].id,\n                    memberID: signature.MemberID,\n                    signatureData: signature.SignatureData,\n                    trainingSessionID: TrainingID\n                });\n            } else {\n                // mutationCreateMemberTrainingSignature\n                await memberTraining_SignatureModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)(),\n                    memberID: signature.MemberID,\n                    signatureData: signature.SignatureData,\n                    trainingSessionID: TrainingID\n                });\n            }\n        } else {\n            await queryGetMemberTrainingSignatures({\n                variables: {\n                    filter: {\n                        memberID: {\n                            eq: signature.MemberID\n                        },\n                        trainingSessionID: {\n                            in: TrainingID\n                        }\n                    }\n                }\n            }).then((response)=>{\n                const data = response.data.readMemberTraining_Signatures.nodes;\n                if (data.length > 0) {\n                    mutationUpdateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                id: data[0].id,\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                } else {\n                    if (signature.SignatureData) {\n                        mutationCreateMemberTrainingSignature({\n                            variables: {\n                                input: {\n                                    memberID: signature.MemberID,\n                                    signatureData: signature.SignatureData,\n                                    trainingSessionID: TrainingID\n                                }\n                            }\n                        });\n                    }\n                }\n            }).catch((error)=>{\n                console.error(\"mutationGetMemberTrainingSignatures error\", error);\n            });\n        }\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(new Date(date.toString()).toLocaleDateString());\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        console.log(\"handleTrainerChange (logbook) - received trainer:\", trainer);\n        if (!trainer) return; // Add early return if trainer is null\n        // With the updated CrewDropdown, trainer should now be a full object with label and value\n        // But we'll still handle the legacy case where it might just be an ID or array\n        const trainerValue = Array.isArray(trainer) ? trainer.length > 0 ? trainer[0].value || trainer[0] : null : trainer.value || trainer;\n        if (!trainerValue) {\n            return;\n        }\n        // Create a proper trainer object with the correct label format\n        const trainerObject = Array.isArray(trainer) ? trainer[0] : trainer.label ? trainer // Use the object as is if it has a label\n         : {\n            value: trainerValue,\n            label: trainer.profile ? \"\".concat(trainer.profile.firstName || \"\", \" \").concat(trainer.profile.surname || \"\") : \"Trainer \".concat(trainerValue)\n        };\n        console.log(\"handleTrainerChange (logbook) - created trainerObject:\", trainerObject);\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainerValue);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainerValue,\n            Members: members\n        });\n        // Add the trainer to the selectedMemberList if not already present\n        const trainerExists = selectedMemberList.some((member)=>member.value === trainerValue);\n        if (!trainerExists) {\n            console.log(\"Adding trainer to selectedMemberList (logbook):\", trainerObject);\n            setSelectedMemberList([\n                ...selectedMemberList,\n                trainerObject\n            ]);\n            setSignatureMembers([\n                ...signatureMembers,\n                {\n                    MemberID: +trainerValue,\n                    SignatureData: null\n                }\n            ]);\n        }\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        // Update training state with selected types\n        const selectedTypes = !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingTypes) ? trainingTypes.map((item)=>item.value) : [];\n        setTraining({\n            ...training,\n            TrainingTypes: selectedTypes\n        });\n        // Clear error message if valid selection is made\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(selectedTypes)) {\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"\"\n                }));\n        }\n    };\n    const handleMemberChange = (members)=>{\n        console.log(\"handleMemberChange (logbook) - received members:\", members);\n        // Ensure members is an array\n        const membersArray = Array.isArray(members) ? members : [\n            members\n        ].filter(Boolean);\n        // Make sure we're filtering with valid member values\n        const signatures = signatureMembers.filter((item)=>membersArray.some((m)=>m && m.value && +m.value === item.MemberID));\n        // Extract member values safely\n        const memberValues = membersArray.filter((item)=>item && item.value).map((item)=>item.value);\n        setTraining({\n            ...training,\n            Members: memberValues\n        });\n        console.log(\"handleMemberChange (logbook) - setting selectedMemberList to:\", membersArray);\n        setSelectedMemberList(membersArray);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (signature, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (signature) {\n            if (index !== -1) {\n                if (signature.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = signature;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: signature\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    }\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel.value\n        });\n    };\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            // getTripEvent\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                setTrainingID(event.crewTrainingID);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                setTrainingID(event.crewTraining.id);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                handleSetTraining(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    const loadTrainingSession = async ()=>{\n        if (offline) {\n            // queryTrainingSessionByID\n            const data = await trainingSessionModel.getById(trainingID);\n            if (data) {\n                handleSetTraining(data);\n            }\n        } else {\n            await queryTrainingSessionByID({\n                variables: {\n                    id: +trainingID\n                }\n            });\n        }\n    };\n    const handleStartTimeChange = (date)=>{\n        setStartTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const handleFinishTimeChange = (date)=>{\n        setFinishTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0 && ((_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTraining({\n                ...training,\n                GeoLocationID: +value.value,\n                Lat: null,\n                Long: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTraining({\n                ...training,\n                GeoLocationID: 0,\n                Lat: value.latitude,\n                Long: value.longitude\n            });\n        }\n    };\n    const handleSetCurrentLocation = (value)=>{\n        setTraining({\n            ...training,\n            GeoLocationID: 0,\n            Lat: value.latitude,\n            Long: value.longitude\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        selectedEvent,\n        currentEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (+trainingID > 0) {\n            loadTrainingSession();\n        }\n    }, [\n        trainingID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(training)) {\n            setTraining({\n                ...training,\n                VesselID: vesselId\n            });\n        }\n    }, [\n        training\n    ]);\n    const offlineUseEffect = async ()=>{\n        // getTrainingTypeByID(trainingTypeId, setTraining)\n        const training = await trainingTypeModel.getById(trainingTypeId);\n        setTraining(training);\n        // getTrainingTypes(setTrainingTypes)\n        const types = await trainingTypeModel.getAll();\n        setTrainingTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (offline) {\n            offlineUseEffect();\n        }\n    }, [\n        offline\n    ]);\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: type.customisedComponentField.nodes\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        var _rawTraining_procedureFields;\n        if (!trainingID) {\n            const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n            setBufferProcedureCheck([\n                ...procedureCheck,\n                {\n                    fieldId: field.id,\n                    status: status\n                }\n            ]);\n            return;\n        }\n        const nodes = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes;\n        const existingField = nodes === null || nodes === void 0 ? void 0 : nodes.find((procedureField)=>procedureField.customisedComponentFieldID === field.id);\n        if (!nodes || !existingField) {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n            return;\n        }\n        if (nodes.length > 0 && existingField) {\n            const fieldID = existingField.id;\n            updateCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        id: +fieldID,\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n        } else {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n        }\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setCurrentFieldComment(fieldComment);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        if (!trainingID) {\n            const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n            setBufferFieldComment([\n                ...fieldComment,\n                {\n                    fieldId: currentField.id,\n                    comment: currentComment\n                }\n            ]);\n            setOpenCommentAlert(false);\n            return;\n        }\n        if (currentFieldComment) {\n            var _rawTraining_procedureFields;\n            updateCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        id: currentFieldComment.id,\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: currentField.id,\n                        comment: currentComment\n                    }\n                }\n            });\n            const nodes = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes;\n            if (nodes) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== currentField.id),\n                            {\n                                ...currentFieldComment,\n                                comment: currentComment\n                            }\n                        ]\n                    }\n                });\n            }\n        } else {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: currentField.id,\n                        comment: currentComment\n                    }\n                }\n            });\n        }\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1337,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Name of trainer\",\n                        disabled: locked,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                disabled: locked,\n                                offline: offline,\n                                value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                onChange: handleTrainerChange,\n                                memberIdOptions: memberIdOptions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1341,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                className: \"text-destructive\",\n                                children: hasFormErrors && formErrors.TrainerID\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1348,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1340,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Crew trained\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            value: training === null || training === void 0 ? void 0 : training.Members,\n                            onChange: handleMemberChange,\n                            memberIdOptions: memberIdOptions\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1353,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1352,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Training types\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-6 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    offline: offline,\n                                    value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                    onChange: handleTrainingTypeChange,\n                                    locked: locked\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1363,\n                                    columnNumber: 29\n                                }, undefined),\n                                formErrors.TrainingTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"text-destructive mt-1\",\n                                    children: formErrors.TrainingTypes\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1370,\n                                    columnNumber: 33\n                                }, undefined),\n                                training && trainingTypes.filter((type)=>{\n                                    var _training_TrainingTypes;\n                                    return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                    variant: \"primary\",\n                                    className: \"w-fit flex-1\",\n                                    onClick: ()=>setOpenViewProcedure(true),\n                                    children: \"View Procedures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1382,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1362,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1361,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Location of training\",\n                        disabled: locked,\n                        children: vesselList && (rawTraining || trainingID === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                            options: vesselList || [],\n                            value: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? vesselList === null || vesselList === void 0 ? void 0 : vesselList.filter((vessel)=>+vessel.value === +(training === null || training === void 0 ? void 0 : training.VesselID))[0] : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? {\n                                label: \"Desktop/shore\",\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType\n                            } : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? {\n                                label: \"Other\",\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType\n                            } : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? {\n                                label: rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.title,\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation2 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation2 === void 0 ? void 0 : _rawTraining_trainingLocation2.id\n                            } : (vesselList === null || vesselList === void 0 ? void 0 : vesselList.find((vessel)=>+vessel.value === +vesselId)) || null,\n                            onChange: handleTrainingVesselChange,\n                            placeholder: \"Select location\",\n                            isDisabled: true,\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1395,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1393,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        className: \"text-destructive\",\n                        children: hasFormErrors && formErrors.VesselID\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1441,\n                        columnNumber: 21\n                    }, undefined),\n                    displayField(\"CrewTraining_FuelLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Fuel level at end of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_22__.Input, {\n                            id: \"fuel-level\",\n                            name: \"fuel-level\",\n                            type: \"number\",\n                            defaultValue: training === null || training === void 0 ? void 0 : training.FuelLevel,\n                            className: \"w-full\",\n                            placeholder: \"Fuel level\",\n                            onChange: lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()(function(e) {\n                                setTraining({\n                                    ...training,\n                                    FuelLevel: e.target.value\n                                });\n                            }, 600)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1449,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1446,\n                        columnNumber: 25\n                    }, undefined),\n                    displayField(\"CrewTraining_StartTime\") || displayField(\"CrewTraining_FinishTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Training duration\",\n                        className: \"mb-1\",\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1467,\n                        columnNumber: 29\n                    }, undefined),\n                    displayField(\"CrewTraining_StartTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Start time of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            time: startTime,\n                            handleTimeChange: handleStartTimeChange,\n                            timeID: \"startTime\",\n                            fieldName: \"Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1476,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1475,\n                        columnNumber: 25\n                    }, undefined),\n                    displayField(\"CrewTraining_FinishTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"End time of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            time: finishTime,\n                            handleTimeChange: handleFinishTimeChange,\n                            timeID: \"finishTime\",\n                            fieldName: \"Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1487,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1486,\n                        columnNumber: 25\n                    }, undefined),\n                    (!currentEvent || training) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        id: \"TrainingSummary\",\n                        placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                        handleEditorChange: handleEditorChange,\n                        content: content,\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1497,\n                        columnNumber: 25\n                    }, undefined),\n                    selectedMemberList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                children: \"Participant Signatures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1516,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                children: selectedMemberList.map((member, index)=>{\n                                    var _signatureMembers_find, _signatureMembers_find1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        title: member.label,\n                                        member: member.label,\n                                        memberId: member.value,\n                                        onSignatureChanged: onSignatureChanged,\n                                        signature: {\n                                            signatureData: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.SignatureData,\n                                            id: (_signatureMembers_find1 = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find1 === void 0 ? void 0 : _signatureMembers_find1.ID\n                                        },\n                                        locked: locked\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                        lineNumber: 1520,\n                                        columnNumber: 41\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1517,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end pb-4 pt-4 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                variant: \"back\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                                onClick: closeModal,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1549,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                variant: \"primary\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                                onClick: locked ? ()=>{} : handleSave,\n                                disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                                children: trainingID === 0 ? \"Save\" : \"Update\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1555,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1548,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1339,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-3/4 sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1572,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1571,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetBody, {\n                            children: training && trainingTypes.filter((type)=>{\n                                var _training_TrainingTypes;\n                                return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                            }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-md p-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium leading-6 mb-4\",\n                                            children: type.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                            lineNumber: 1587,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            dangerouslySetInnerHTML: {\n                                                __html: type.procedure\n                                            }\n                                        }, type.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                            lineNumber: 1590,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, type.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1584,\n                                    columnNumber: 37\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1574,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                    lineNumber: 1570,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1569,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n        lineNumber: 1335,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingEvent, \"IFIVVo0IGCE+7GWuQeWlOiL77ts=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation\n    ];\n});\n_c = CrewTrainingEvent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingEvent);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingEvent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\n"));

/***/ })

});