"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/weather/forecast.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _logbook_components_location__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logbook/components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/weatherHelper */ \"(app-pages-browser)/./src/app/helpers/weatherHelper.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _forecast_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forecast-list */ \"(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_barometer_widget__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./widgets/barometer-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/barometer-widget.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecast = (param)=>{\n    let { logBookEntryID, offline = false, locked } = param;\n    _s();\n    const [isWriteModeForecast, setIsWriteModeForecast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isManualEntry, setIsManualEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forecast, setForecast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Current location state for the location field\n    const [currentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: null,\n        longitude: null\n    });\n    const [selectedCoordinates, setSelectedCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: null,\n        longitude: null\n    });\n    const isOnline = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_24__.useOnline)();\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const [isStormGlassLoading, setIsStormGlassLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshList, setRefreshList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [geoLocations, setGeoLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getTimeNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"HH:mm\");\n    };\n    const [getGeoLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_12__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readGeoLocations.nodes;\n            if (data) {\n                setGeoLocations(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryGeoLocations error\", error);\n        }\n    });\n    const getDayNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\");\n    };\n    const initForecast = ()=>{\n        setForecast({\n            id: 0,\n            time: getTimeNow(),\n            day: getDayNow(),\n            geoLocationID: 0,\n            lat: 0,\n            long: 0,\n            logBookEntryID: logBookEntryID\n        });\n    };\n    const createForecast = ()=>{\n        initForecast();\n        setIsWriteModeForecast(true);\n    };\n    const handleSetCurrentLocation = (value)=>{\n        setForecast({\n            ...forecast,\n            geoLocationID: 0,\n            lat: value.latitude,\n            long: value.longitude\n        });\n        setSelectedCoordinates({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setForecast({\n                ...forecast,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, use them directly\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setSelectedCoordinates({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            } else {\n                // Otherwise find the location in geoLocations by ID\n                geoLocations.find((item)=>{\n                    if (item.id == +value.value) {\n                        setSelectedCoordinates({\n                            latitude: item.lat,\n                            longitude: item.long\n                        });\n                        return true;\n                    }\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setForecast({\n                ...forecast,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update selected coordinates\n            setSelectedCoordinates({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    };\n    const processStormGlassData = (data)=>{\n        const { windSpeed, windDirection, swellHeight, visibility, precipitation, pressure, cloudCover } = data.hours[0];\n        const windSpeedInKnots = (windSpeed ? (windSpeed.noaa || windSpeed.sg || 0) / 0.51444 : 0).toFixed(0) // Convert m/s to knot. One knot is equal to approximately 0.51444 meters per second (m/s).\n        ;\n        const compassWindDirection = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getWindDirection)(windDirection ? windDirection.noaa || windDirection.sg || 0 : 0) // convert degrees to compass direction\n        ;\n        const swellValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getSwellHeightRange)(swellHeight ? swellHeight.noaa || swellHeight.sg || 0 : 0);\n        const visibilityValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getVisibility)(visibility ? visibility.noaa || visibility.sg || 0 : 0);\n        const precipitationValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getPrecipitation)(precipitation ? precipitation.noaa || precipitation.sg || 0 : 0);\n        const pressureValue = pressure ? pressure.noaa || pressure.sg || 0 : 0;\n        const cloudCoverValue = (cloudCover ? cloudCover.noaa || cloudCover.sg || 0 : 0).toFixed(0);\n        setForecast({\n            ...forecast,\n            windSpeed: +windSpeedInKnots,\n            windDirection: compassWindDirection,\n            swell: swellValue,\n            visibility: visibilityValue,\n            precipitation: precipitationValue,\n            pressure: +pressureValue,\n            cloudCover: +cloudCoverValue\n        });\n        setIsStormGlassLoading(false);\n    };\n    const isStormGlassButtonEnabled = ()=>{\n        let isStormGlassButtonEnabled = false;\n        if (+forecast.geoLocationID > 0) {\n            isStormGlassButtonEnabled = true;\n        } else if (!isNaN(+forecast.lat) || !isNaN(+forecast.long)) {\n            isStormGlassButtonEnabled = true;\n        }\n        if (!isOnline) {\n            isStormGlassButtonEnabled = false;\n        }\n        return isStormGlassButtonEnabled;\n    };\n    const getStormGlassData = ()=>{\n        setIsManualEntry(false);\n        if (forecast.geoLocationID > 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.loading(\"Retrieving forecast...\");\n            setIsStormGlassLoading(true);\n            const dateString = \"\".concat(forecast.day, \" \").concat(forecast.time);\n            let startDate = new Date(dateString);\n            let endDate = startDate;\n            var headers = {\n                \"Cache-Control\": \"no-cache\",\n                Authorization: \"480c5714-38bc-11ea-acb4-0242ac130002-480c58fe-38bc-11ea-acb4-0242ac130002\" || 0,\n                \"Access-Control-Allow-Credentials\": \"true\"\n            };\n            var params = \"windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover\";\n            const url = \"https://api.stormglass.io/v2/weather/point?lat=\".concat(selectedCoordinates.latitude || 0, \"&lng=\").concat(selectedCoordinates.longitude || 0, \"&params=\").concat(params, \"&start=\").concat(startDate.toISOString(), \"&end=\").concat(endDate.toISOString());\n            let request = fetch(url, {\n                method: \"GET\",\n                headers\n            });\n            request.then((response)=>response.json()).then((jsonData)=>{\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Forecast retrieved successfully\");\n                processStormGlassData(jsonData);\n            }).catch((error)=>{\n                setIsStormGlassLoading(false);\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                console.error(\"Catch error:\", error);\n            });\n            return request;\n        } else {\n            if (\"geolocation\" in navigator) {\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.loading(\"Retrieving forecast...\");\n                setIsStormGlassLoading(true);\n                return new Promise((resolve, reject)=>{\n                    return navigator.geolocation.getCurrentPosition(()=>{\n                        const dateString = \"\".concat(forecast.day, \" \").concat(forecast.time);\n                        let startDate = new Date(dateString);\n                        let endDate = startDate;\n                        var headers = {\n                            \"Cache-Control\": \"no-cache\",\n                            Authorization: \"480c5714-38bc-11ea-acb4-0242ac130002-480c58fe-38bc-11ea-acb4-0242ac130002\" || 0,\n                            \"Access-Control-Allow-Credentials\": \"true\"\n                        };\n                        var params = \"windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover\";\n                        const url = \"https://api.stormglass.io/v2/weather/point?lat=\".concat(selectedCoordinates.latitude || 0, \"&lng=\").concat(selectedCoordinates.longitude || 0, \"&params=\").concat(params, \"&start=\").concat(startDate.toISOString(), \"&end=\").concat(endDate.toISOString());\n                        let request = fetch(url, {\n                            method: \"GET\",\n                            headers\n                        });\n                        request.then((response)=>response.json()).then((jsonData)=>{\n                            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Forecast retrieved successfully\");\n                            processStormGlassData(jsonData);\n                            resolve(jsonData);\n                        }).catch((error)=>{\n                            setIsStormGlassLoading(false);\n                            reject(error);\n                            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                            console.error(\"Catch error:\", error);\n                        });\n                        return request;\n                    }, (error)=>{\n                        setIsStormGlassLoading(false);\n                        reject(error);\n                        sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                        console.error(\"Geolocation error\", error);\n                    });\n                });\n            } else {\n                setIsStormGlassLoading(false);\n                console.error(\"Geolocation is not supported by your browser\");\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"Geolocation is not supported by your browser\");\n            }\n        }\n    };\n    const handleOnChangePressure = (value)=>{\n        const pressure = Array.isArray(value) ? value[0] : value;\n        setForecast({\n            ...forecast,\n            pressure: pressure\n        });\n    };\n    const handleOnChangeSeaSwell = (item)=>{\n        if (item) {\n            setForecast({\n                ...forecast,\n                swell: item.value\n            });\n        }\n    };\n    const handleWindChange = (values)=>{\n        // Update both wind speed and direction in the forecast state\n        setForecast({\n            ...forecast,\n            windSpeed: values.speed,\n            windDirection: values.direction.value\n        });\n    };\n    const handleSetComment = lodash_debounce__WEBPACK_IMPORTED_MODULE_5___default()((item)=>{\n        setForecast({\n            ...forecast,\n            comment: item\n        });\n    }, 600);\n    const [createWeatherForecast, { loading: createWeatherForecastLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CreateWeatherForecast, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"CreateWeatherForecast Error\", error);\n        }\n    });\n    const [updateWeatherForecast, { loading: updateWeatherForecastLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast Error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        if (+forecast.id === 0) {\n            if (offline) {\n                await forecastModel.save({\n                    ...forecast,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n                });\n                setIsWriteModeForecast(false);\n                setRefreshList(true);\n            } else {\n                await createWeatherForecast({\n                    variables: {\n                        input: {\n                            ...forecast\n                        }\n                    }\n                });\n            }\n        } else {\n            if (forecast.geoLocation) delete forecast.geoLocation;\n            if (forecast.__typename) delete forecast.__typename;\n            if (offline) {\n                await forecastModel.save({\n                    ...forecast,\n                    day: dayjs__WEBPACK_IMPORTED_MODULE_2___default()(forecast.day).format(\"YYYY-MM-DD\")\n                });\n                setIsWriteModeForecast(false);\n                setRefreshList(true);\n            } else {\n                await updateWeatherForecast({\n                    variables: {\n                        input: {\n                            ...forecast\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const handleCancel = ()=>{\n        initForecast();\n        setIsWriteModeForecast(false);\n    };\n    const handleForecastClick = (forecast)=>{\n        if (locked) {\n            return;\n        }\n        setIsManualEntry(false);\n        const newForecast = {\n            ...forecast,\n            time: formatTime(forecast.time),\n            day: dayjs__WEBPACK_IMPORTED_MODULE_2___default()(forecast.day.toString()).format(\"YYYY-MM-DD\")\n        };\n        setForecast(newForecast);\n        setIsWriteModeForecast(true);\n    };\n    const [deleteWeatherForecasts, { loading: deleteWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DeleteWeatherForecasts, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"DeleteWeatherForecasts Error\", error);\n        }\n    });\n    const handleDeleteForecast = async ()=>{\n        if (offline) {\n            forecastModel.delete(forecast);\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        } else {\n            await deleteWeatherForecasts({\n                variables: {\n                    ids: [\n                        forecast.id\n                    ]\n                }\n            });\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const handleOnChangeCloudWidget = (item)=>{\n        setForecast({\n            ...forecast,\n            visibility: item.visibility.value,\n            precipitation: item.precipitation.value,\n            cloudCover: item.cloudCover\n        });\n    };\n    // Format time string for display\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logBookEntryID > 0) {\n            setForecast({\n                ...forecast,\n                logBookEntryID: logBookEntryID\n            });\n        }\n        getGeoLocations();\n    }, [\n        logBookEntryID\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_18__.Card, {\n        children: [\n            !isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.H2, {\n                                children: \"Weather forecast\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                                children: \"You can start by retrieving a weather forecast up to 7-days into the future. SeaLogs currently using the Stormglass API with more forecasting services coming soon.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                                children: \"After retrieving a forecast you can add your own observations. We use this data to compare the accuracy of forecasts plus share weather observations with our community of users.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                            disabled: locked,\n                            onClick: ()=>createForecast(),\n                            children: \"Add another forecast\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true),\n            isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            id: \"forecast-date\",\n                                            name: \"forecast-date\",\n                                            label: \"Date and time of forecast required\",\n                                            value: forecast.day && forecast.time ? new Date(\"\".concat(forecast.day, \"T\").concat(forecast.time)) : undefined,\n                                            mode: \"single\",\n                                            type: \"datetime\" // Keep datetime to include time picker\n                                            ,\n                                            onChange: (date)=>{\n                                                if (date) {\n                                                    const newDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date);\n                                                    setForecast({\n                                                        ...forecast,\n                                                        day: newDate.format(\"YYYY-MM-DD\"),\n                                                        time: newDate.format(\"HH:mm:00\")\n                                                    });\n                                                }\n                                            },\n                                            dateFormat: \"dd MMM,\",\n                                            timeFormat: \"HH:mm\" // Explicitly set time format\n                                            ,\n                                            placeholder: \"Time\",\n                                            closeOnSelect: false,\n                                            clearable: true,\n                                            icon: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                            className: \"w-full\",\n                                            includeTime: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2 flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            label: \"Location for forecast\",\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_components_location__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    offline: offline,\n                                                    setCurrentLocation: handleSetCurrentLocation,\n                                                    handleLocationChange: (e)=>{\n                                                        handleLocationChange(e);\n                                                    },\n                                                    currentEvent: {\n                                                        geoLocationID: forecast.geoLocationID,\n                                                        lat: forecast.lat,\n                                                        long: forecast.long\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    isWriteModeForecast && isStormGlassButtonEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            onClick: ()=>getStormGlassData(),\n                                            disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                            className: \"w-full\",\n                                            children: isStormGlassLoading ? \"Retrieving forecast...\" : \"Retrieve forecast (API)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            variant: \"secondary\",\n                                            onClick: ()=>setIsManualEntry(true),\n                                            className: \"w-full\",\n                                            children: \"OR enter a manual forecast (observation)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: isManualEntry ? \"flex flex-col gap-4\" : \"grid grid-cols-2 gap-2 sm:grid-cols-4 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        speed: forecast.windSpeed,\n                                        direction: forecast.windDirection,\n                                        onChange: handleWindChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        visibilityValue: forecast.visibility,\n                                        precipitationValue: forecast.precipitation,\n                                        cloudCoverValue: forecast.cloudCover,\n                                        onChange: handleOnChangeCloudWidget\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        value: forecast.swell,\n                                        onChange: handleOnChangeSeaSwell\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_barometer_widget__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        value: forecast.pressure,\n                                        editMode: isManualEntry,\n                                        onChange: handleOnChangePressure\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 25\n                            }, undefined),\n                            !isManualEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-[10px]\",\n                                children: \"Forecast provided by Stormglass API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    label: \"Your comments\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__.Textarea, {\n                                        id: \"forecast-comment\",\n                                        rows: 4,\n                                        placeholder: \"Comments ...\",\n                                        defaultValue: forecast.comment || \"\",\n                                        onChange: (e)=>handleSetComment(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"flex flex-col standard:flex-row  sm:justify-end mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                        variant: \"back\",\n                                        onClick: handleCancel,\n                                        disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    +forecast.id > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                        variant: \"destructive\",\n                                        iconLeft: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                        onClick: ()=>{\n                                            setDeleteDialogOpen(true);\n                                        },\n                                        disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                        children: \"Delete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                iconLeft: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                onClick: handleSave,\n                                disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                children: \"\".concat(+forecast.id === 0 ? \"Save\" : \"Update\", \" forecast\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 518,\n                columnNumber: 17\n            }, undefined),\n            !isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forecast_list__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    logBookEntryID: logBookEntryID,\n                    refreshList: refreshList,\n                    onClick: handleForecastClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                    lineNumber: 715,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 714,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_14__.AlertDialogNew, {\n                openDialog: deleteDialogOpen,\n                setOpenDialog: setDeleteDialogOpen,\n                handleCreate: handleDeleteForecast,\n                title: \"Delete Forecast Data\",\n                description: \"Are you sure you want to delete the forecast data for \".concat(forecast.time ? formatTime(forecast.time) : \"\", \" / \").concat(forecast.day ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_11__.formatDate)(forecast.day) : \"\", \"?\"),\n                variant: \"danger\",\n                actionText: \"Delete\",\n                cancelText: \"Cancel\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 724,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n        lineNumber: 490,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecast, \"7Fqh81ICgSbMHVlaGs6jrH67kdg=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_24__.useOnline,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation\n    ];\n});\n_c = WeatherForecast;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecast);\nvar _c;\n$RefreshReg$(_c, \"WeatherForecast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast.tsx\n"));

/***/ })

});