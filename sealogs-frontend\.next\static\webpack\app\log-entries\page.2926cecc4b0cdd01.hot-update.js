"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/signature-pad.tsx":
/*!******************************************!*\
  !*** ./src/components/signature-pad.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_signature_canvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-signature-canvas */ \"(app-pages-browser)/./node_modules/.pnpm/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1/node_modules/react-signature-canvas/build/index.js\");\n/* harmony import */ var react_signature_canvas__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_signature_canvas__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eraser.js\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SignaturePad = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = _s((param)=>{\n    let { signature, member, memberId, title, onSignatureChanged, penColor = \"blue\", className, description, locked = false, ...canvasProps } = param;\n    _s();\n    const padRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var _signature_signatureData;\n    const [dataUrl, setDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_signature_signatureData = signature === null || signature === void 0 ? void 0 : signature.signatureData) !== null && _signature_signatureData !== void 0 ? _signature_signatureData : null);\n    // 1. If we have a valid ID but no inline data, fetch it\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ((signature === null || signature === void 0 ? void 0 : signature.id) && signature.id > 0 && !signature.signatureData) {\n            (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getSignatureUrl)(signature.id).then(setDataUrl).catch((err)=>console.error(\"Fetch sig URL failed:\", err));\n        }\n    }, [\n        signature === null || signature === void 0 ? void 0 : signature.id,\n        signature === null || signature === void 0 ? void 0 : signature.signatureData\n    ]);\n    // 2. Whenever dataUrl updates, load it into the pad\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const pad = padRef.current;\n        if (pad && dataUrl) {\n            pad.clear();\n            pad.fromDataURL(dataUrl);\n        }\n    }, [\n        dataUrl\n    ]);\n    const handleEnd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const pad = padRef.current;\n        if (pad && !pad.isEmpty()) {\n            const url = pad.toDataURL();\n            onSignatureChanged(url, member, memberId);\n        }\n    }, [\n        onSignatureChanged,\n        member,\n        memberId\n    ]);\n    const handleClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const pad = padRef.current;\n        pad === null || pad === void 0 ? void 0 : pad.clear();\n        onSignatureChanged(\"\", member, memberId);\n        setDataUrl(null);\n    }, [\n        onSignatureChanged,\n        member,\n        memberId\n    ]);\n    const displayTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _ref;\n        return (_ref = title !== null && title !== void 0 ? title : member) !== null && _ref !== void 0 ? _ref : \"Signature\";\n    }, [\n        title,\n        member\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative w-full space-y-2\", className),\n        children: [\n            locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                variant: \"destructive\",\n                className: \"absolute top-2 right-2 text-xs gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Locked\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 94,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui__WEBPACK_IMPORTED_MODULE_5__.P, {\n                    className: \"text-sm text-muted-foreground\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 102,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                htmlFor: \"sig-canvas\",\n                label: displayTitle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_signature_canvas__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    ...canvasProps,\n                    ref: padRef,\n                    penColor: penColor,\n                    canvasProps: {\n                        id: \"sig-canvas\",\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"border-2 border-dashed border-outer-space-400 rounded-lg h-48\", locked ? \"bg-muted/50\" : \"bg-white\"),\n                        style: {\n                            width: \"100%\",\n                            touchAction: locked ? \"none\" : \"auto\",\n                            cursor: locked ? \"not-allowed\" : \"crosshair\"\n                        }\n                    },\n                    onEnd: handleEnd\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 111,\n                columnNumber: 17\n            }, undefined),\n            locked && // an overlay to prevent drawing when locked\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white/60 rounded-lg pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 134,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 25\n                            }, undefined),\n                            \"Draw your signature\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        size: \"sm\",\n                        iconLeft: _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        className: \"w-fit\",\n                        onClick: handleClear,\n                        disabled: locked,\n                        \"aria-label\": \"Clear signature\",\n                        children: \"Clear\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 137,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n        lineNumber: 92,\n        columnNumber: 13\n    }, undefined);\n}, \"TJjvpSMRJ7e9XwkzR4OfCEZZTXM=\")), \"TJjvpSMRJ7e9XwkzR4OfCEZZTXM=\");\n_c1 = SignaturePad;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SignaturePad);\nvar _c, _c1;\n$RefreshReg$(_c, \"SignaturePad$React.memo\");\n$RefreshReg$(_c1, \"SignaturePad\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/signature-pad.tsx\n"));

/***/ })

});