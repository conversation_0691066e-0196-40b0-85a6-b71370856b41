"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/logbook/forms/crew-training-event.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _crew_training_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../crew-training/type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/models/trainingType */ \"(app-pages-browser)/./src/app/offline/models/trainingType.js\");\n/* harmony import */ var _app_offline_models_memberTraining_Signature__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/offline/models/memberTraining_Signature */ \"(app-pages-browser)/./src/app/offline/models/memberTraining_Signature.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_trainingSessionDue__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/trainingSessionDue */ \"(app-pages-browser)/./src/app/offline/models/trainingSessionDue.js\");\n/* harmony import */ var _app_offline_models_trainingSession__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/trainingSession */ \"(app-pages-browser)/./src/app/offline/models/trainingSession.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingEvent = (param)=>{\n    let { trainingTypeId = 0, vesselId = 0, selectedEvent = false, currentTrip = false, closeModal, updateTripReport, tripReport, crewMembers, masterID, logBookConfig, vessels, locked, offline = false, logBookStartDate } = param;\n    var _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _rawTraining_trainingLocation2;\n    _s();\n    const [trainingID, setTrainingID] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(selectedEvent);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({});\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Date().toLocaleDateString());\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery)(\"(min-width: 640px)\");\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [vesselList, setVesselList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [openDescriptionPanel, setOpenDescriptionPanel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [descriptionPanelContent, setDescriptionPanelContent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [descriptionPanelHeading, setDescriptionPanelHeading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [currentFieldComment, setCurrentFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const [finishTime, setFinishTime] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const memberIdOptions = [\n        masterID,\n        ...Array.isArray(crewMembers) ? crewMembers.map((m)=>m.crewMemberID) : []\n    ];\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const trainingTypeModel = new _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_16__[\"default\"]();\n    const memberTraining_SignatureModel = new _app_offline_models_memberTraining_Signature__WEBPACK_IMPORTED_MODULE_17__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const trainingSessionDueModel = new _app_offline_models_trainingSessionDue__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const trainingSessionModel = new _app_offline_models_trainingSession__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypes)(setTrainingTypes);\n    }\n    const handleSetTraining = (t)=>{\n        const tDate = new Date(t.date).toLocaleDateString();\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(t.date).format(\"YYYY-MM-DD\"),\n            Members: t.members.nodes.map((m)=>m.id),\n            TrainerID: t.trainer.id,\n            trainingSummary: t.trainingSummary,\n            TrainingTypes: t.trainingTypes.nodes.map((t)=>t.id),\n            VesselID: vesselId,\n            FuelLevel: t.fuelLevel || 0,\n            GeoLocationID: t.geoLocationID,\n            StartTime: t.startTime,\n            FinishTime: t.finishTime,\n            Lat: t.lat,\n            Long: t.long\n        };\n        setContent(t.trainingSummary);\n        setStartTime(t.startTime);\n        setFinishTime(t.finishTime);\n        setRawTraining(t);\n        setTraining(trainingData);\n        if (+t.geoLocationID > 0) {\n            setCurrentLocation({\n                latitude: t.geoLocation.lat,\n                longitude: t.geoLocation.long\n            });\n        } else {\n            setCurrentLocation({\n                latitude: t.lat,\n                longitude: t.long\n            });\n        }\n        const members = t.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        setSelectedMemberList(members);\n        const signatures = t.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    // const handleSetVessels = (data: any) => {\n    //     const activeVessels = data?.filter((vessel: any) => !vessel.archived)\n    //     const formattedData = [\n    //         {\n    //             label: 'Other',\n    //             value: 'Other',\n    //         },\n    //         {\n    //             label: 'Desktop/shore',\n    //             value: 'Onshore',\n    //         },\n    //         ...activeVessels.map((vessel: any) => ({\n    //             value: vessel.id,\n    //             label: vessel.title,\n    //         })),\n    //     ]\n    //     setVessels(formattedData)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (vessels) {\n            const activeVessels = vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>!vessel.archived);\n            const formattedData = [\n                {\n                    label: \"Other\",\n                    value: \"Other\"\n                },\n                {\n                    label: \"Desktop/shore\",\n                    value: \"Onshore\"\n                },\n                ...activeVessels.map((vessel)=>({\n                        value: vessel.id,\n                        label: vessel.title\n                    }))\n            ];\n            setVesselList(formattedData);\n        }\n    }, [\n        vessels\n    ]);\n    // const [queryVessels] = useLazyQuery(VESSEL_LIST, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (queryVesselResponse: any) => {\n    //         if (queryVesselResponse.readVessels.nodes) {\n    //             handleSetVessels(queryVesselResponse.readVessels.nodes)\n    //         }\n    //     },\n    //     onError: (error: any) => {\n    //         console.error('queryVessels error', error)\n    //     },\n    // })\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                setTrainingID(data.id);\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id),\n                            eventCategory: \"CrewTraining\",\n                            crewTrainingID: data.id\n                        }\n                    }\n                });\n                closeModal();\n            } else {\n                console.error(\"mutationCreateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        if (offline) {\n            const allDues = await trainingSessionDueModel.getAll();\n            const data = allDues.filter((item)=>item.memberID === variables.filter.memberID.eq && item.vesselID === variables.filter.vesselID.eq && item.trainingTypeID === variables.filter.trainingTypeID.eq);\n            onCompleted(data);\n        } else {\n            const { data } = await readOneTrainingSessionDue({\n                variables: variables\n            });\n            onCompleted(data.readOneTrainingSessionDue);\n        }\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    if (offline) {\n                        // mutationCreateTrainingSessionDue\n                        await trainingSessionDueModel.save({\n                            ...item,\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)()\n                        });\n                    } else {\n                        await mutationCreateTrainingSessionDue(variables);\n                    }\n                } else {\n                    if (offline) {\n                        // mutationUpdateTrainingSessionDue\n                        await trainingSessionDueModel.save(item);\n                    } else {\n                        await mutationUpdateTrainingSessionDue(variables);\n                    }\n                }\n            }));\n        }\n    };\n    const [updateTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UpdateTripEvent, {\n        onCompleted: (response)=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating trip event\", error);\n        }\n    });\n    const [createTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CreateTripEvent, {\n        onCompleted: (response)=>{\n            const data = response.createTripEvent;\n            setCurrentEvent(data);\n            saveTraining();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip event\", error);\n        }\n    });\n    const saveTraining = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(logBookStartDate).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\",\n            fuelLevel: \"\".concat(training.FuelLevel),\n            geoLocationID: training.GeoLocationID,\n            startTime: startTime,\n            finishTime: finishTime,\n            lat: \"\".concat(training.Lat),\n            long: \"\".concat(training.Long)\n        };\n        if (trainingID === 0) {\n            if (offline) {\n                // mutationCreateTrainingSession\n                const data = await trainingSessionModel.save({\n                    ...input,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)()\n                });\n                setTrainingID(data.id);\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                // updateTripEvent\n                await tripEventModel.save({\n                    id: +(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id),\n                    eventCategory: \"CrewTraining\",\n                    crewTrainingID: data.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                await mutationCreateTrainingSession({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // mutationUpdateTrainingSession\n                const data = await trainingSessionModel.save(input);\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n            } else {\n                await mutationUpdateTrainingSession({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        // Validate Training Types - check if empty or undefined\n        if (!training.TrainingTypes || lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n            // Clear any previous error state for this field\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"Nature of training is required\"\n                }));\n        } else {\n            // Clear any previous error for this field when valid\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"\"\n                }));\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(logBookStartDate).format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            return;\n        }\n        if (currentEvent) {\n            if (offline) {\n                // updateTripEvent\n                await tripEventModel.save({\n                    id: +currentEvent.id,\n                    eventCategory: \"CrewTraining\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +currentEvent.id,\n                            eventCategory: \"CrewTraining\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n            saveTraining();\n            closeModal();\n        } else {\n            if (offline) {\n                // createTripEvent\n                const tripEventData = await tripEventModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)(),\n                    eventCategory: \"CrewTraining\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                setCurrentEvent(tripEventData);\n                saveTraining();\n            } else {\n                createTripEvent({\n                    variables: {\n                        input: {\n                            eventCategory: \"CrewTraining\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        if (offline) {\n            // queryGetMemberTrainingSignatures\n            const allSignatures = await memberTraining_SignatureModel.getAll();\n            const data = allSignatures.filter((item)=>item.memberID === signature.MemberID && item.trainingSessionID === TrainingID);\n            if (data.length > 0) {\n                // mutationUpdateMemberTrainingSignature\n                await memberTraining_SignatureModel.save({\n                    id: data[0].id,\n                    memberID: signature.MemberID,\n                    signatureData: signature.SignatureData,\n                    trainingSessionID: TrainingID\n                });\n            } else {\n                // mutationCreateMemberTrainingSignature\n                await memberTraining_SignatureModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)(),\n                    memberID: signature.MemberID,\n                    signatureData: signature.SignatureData,\n                    trainingSessionID: TrainingID\n                });\n            }\n        } else {\n            await queryGetMemberTrainingSignatures({\n                variables: {\n                    filter: {\n                        memberID: {\n                            eq: signature.MemberID\n                        },\n                        trainingSessionID: {\n                            in: TrainingID\n                        }\n                    }\n                }\n            }).then((response)=>{\n                const data = response.data.readMemberTraining_Signatures.nodes;\n                if (data.length > 0) {\n                    mutationUpdateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                id: data[0].id,\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                } else {\n                    if (signature.SignatureData) {\n                        mutationCreateMemberTrainingSignature({\n                            variables: {\n                                input: {\n                                    memberID: signature.MemberID,\n                                    signatureData: signature.SignatureData,\n                                    trainingSessionID: TrainingID\n                                }\n                            }\n                        });\n                    }\n                }\n            }).catch((error)=>{\n                console.error(\"mutationGetMemberTrainingSignatures error\", error);\n            });\n        }\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(new Date(date.toString()).toLocaleDateString());\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Check if trainer is an array (multiple selection) or a single object\n        const trainerValue = Array.isArray(trainer) ? trainer.length > 0 ? trainer[0].value : null : trainer.value;\n        if (!trainerValue) {\n            return;\n        }\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainerValue);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainerValue,\n            Members: members\n        });\n        // Create a proper trainer object for the selectedMemberList\n        const trainerObject = Array.isArray(trainer) ? trainer[0] : trainer;\n        // Check if trainer is already in selectedMemberList to avoid duplicates\n        const isTrainerAlreadySelected = selectedMemberList.some((member)=>+member.value === +trainerValue);\n        if (!isTrainerAlreadySelected) {\n            setSelectedMemberList([\n                ...selectedMemberList,\n                trainerObject\n            ]);\n            setSignatureMembers([\n                ...signatureMembers,\n                {\n                    MemberID: +trainerValue,\n                    SignatureData: null\n                }\n            ]);\n        }\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        // Update training state with selected types\n        const selectedTypes = !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingTypes) ? trainingTypes.map((item)=>item.value) : [];\n        setTraining({\n            ...training,\n            TrainingTypes: selectedTypes\n        });\n        // Clear error message if valid selection is made\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(selectedTypes)) {\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"\"\n                }));\n        }\n    };\n    const handleMemberChange = (members)=>{\n        // Ensure members is an array\n        const membersArray = Array.isArray(members) ? members : [\n            members\n        ].filter(Boolean);\n        // Make sure we're filtering with valid member values\n        const signatures = signatureMembers.filter((item)=>membersArray.some((m)=>m && m.value && +m.value === item.MemberID));\n        // Extract member values safely\n        const memberValues = membersArray.filter((item)=>item && item.value).map((item)=>item.value);\n        setTraining({\n            ...training,\n            Members: memberValues\n        });\n        setSelectedMemberList(membersArray);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (signature, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (signature) {\n            if (index !== -1) {\n                if (signature.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = signature;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: signature\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    }\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel.value\n        });\n    };\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            // getTripEvent\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                setTrainingID(event.crewTrainingID);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                setTrainingID(event.crewTraining.id);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                handleSetTraining(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    const loadTrainingSession = async ()=>{\n        if (offline) {\n            // queryTrainingSessionByID\n            const data = await trainingSessionModel.getById(trainingID);\n            if (data) {\n                handleSetTraining(data);\n            }\n        } else {\n            await queryTrainingSessionByID({\n                variables: {\n                    id: +trainingID\n                }\n            });\n        }\n    };\n    const handleStartTimeChange = (date)=>{\n        setStartTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const handleFinishTimeChange = (date)=>{\n        setFinishTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0 && ((_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTraining({\n                ...training,\n                GeoLocationID: +value.value,\n                Lat: null,\n                Long: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTraining({\n                ...training,\n                GeoLocationID: 0,\n                Lat: value.latitude,\n                Long: value.longitude\n            });\n        }\n    };\n    const handleSetCurrentLocation = (value)=>{\n        setTraining({\n            ...training,\n            GeoLocationID: 0,\n            Lat: value.latitude,\n            Long: value.longitude\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        selectedEvent,\n        currentEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (+trainingID > 0) {\n            loadTrainingSession();\n        }\n    }, [\n        trainingID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(training)) {\n            setTraining({\n                ...training,\n                VesselID: vesselId\n            });\n        }\n    }, [\n        training\n    ]);\n    const offlineUseEffect = async ()=>{\n        // getTrainingTypeByID(trainingTypeId, setTraining)\n        const training = await trainingTypeModel.getById(trainingTypeId);\n        setTraining(training);\n        // getTrainingTypes(setTrainingTypes)\n        const types = await trainingTypeModel.getAll();\n        setTrainingTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (offline) {\n            offlineUseEffect();\n        }\n    }, [\n        offline\n    ]);\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: type.customisedComponentField.nodes\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        var _rawTraining_procedureFields;\n        if (!trainingID) {\n            const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n            setBufferProcedureCheck([\n                ...procedureCheck,\n                {\n                    fieldId: field.id,\n                    status: status\n                }\n            ]);\n            return;\n        }\n        const nodes = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes;\n        const existingField = nodes === null || nodes === void 0 ? void 0 : nodes.find((procedureField)=>procedureField.customisedComponentFieldID === field.id);\n        if (!nodes || !existingField) {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n            return;\n        }\n        if (nodes.length > 0 && existingField) {\n            const fieldID = existingField.id;\n            updateCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        id: +fieldID,\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n        } else {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n        }\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setCurrentFieldComment(fieldComment);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        if (!trainingID) {\n            const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n            setBufferFieldComment([\n                ...fieldComment,\n                {\n                    fieldId: currentField.id,\n                    comment: currentComment\n                }\n            ]);\n            setOpenCommentAlert(false);\n            return;\n        }\n        if (currentFieldComment) {\n            var _rawTraining_procedureFields;\n            updateCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        id: currentFieldComment.id,\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: currentField.id,\n                        comment: currentComment\n                    }\n                }\n            });\n            const nodes = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes;\n            if (nodes) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== currentField.id),\n                            {\n                                ...currentFieldComment,\n                                comment: currentComment\n                            }\n                        ]\n                    }\n                });\n            }\n        } else {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: currentField.id,\n                        comment: currentComment\n                    }\n                }\n            });\n        }\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1312,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Name of trainer\",\n                        disabled: locked,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                disabled: locked,\n                                offline: offline,\n                                value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                onChange: handleTrainerChange,\n                                memberIdOptions: memberIdOptions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1316,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                className: \"text-destructive\",\n                                children: hasFormErrors && formErrors.TrainerID\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1323,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1315,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Crew trained\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            value: training === null || training === void 0 ? void 0 : training.Members,\n                            onChange: handleMemberChange,\n                            memberIdOptions: memberIdOptions\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1328,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1327,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Training types\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-6 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    offline: offline,\n                                    value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                    onChange: handleTrainingTypeChange,\n                                    locked: locked\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1338,\n                                    columnNumber: 29\n                                }, undefined),\n                                formErrors.TrainingTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"text-destructive mt-1\",\n                                    children: formErrors.TrainingTypes\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1345,\n                                    columnNumber: 33\n                                }, undefined),\n                                training && trainingTypes.filter((type)=>{\n                                    var _training_TrainingTypes;\n                                    return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                    variant: \"primary\",\n                                    className: \"w-fit flex-1\",\n                                    onClick: ()=>setOpenViewProcedure(true),\n                                    children: \"View Procedures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1357,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1337,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1336,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Location of training\",\n                        disabled: locked,\n                        children: vesselList && (rawTraining || trainingID === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                            options: vesselList || [],\n                            value: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? vesselList === null || vesselList === void 0 ? void 0 : vesselList.filter((vessel)=>+vessel.value === +(training === null || training === void 0 ? void 0 : training.VesselID))[0] : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? {\n                                label: \"Desktop/shore\",\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType\n                            } : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? {\n                                label: \"Other\",\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType\n                            } : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? {\n                                label: rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.title,\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation2 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation2 === void 0 ? void 0 : _rawTraining_trainingLocation2.id\n                            } : (vesselList === null || vesselList === void 0 ? void 0 : vesselList.find((vessel)=>+vessel.value === +vesselId)) || null,\n                            onChange: handleTrainingVesselChange,\n                            placeholder: \"Select location\",\n                            isDisabled: true,\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1370,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1368,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        className: \"text-destructive\",\n                        children: hasFormErrors && formErrors.VesselID\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1416,\n                        columnNumber: 21\n                    }, undefined),\n                    displayField(\"CrewTraining_FuelLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Fuel level at end of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_22__.Input, {\n                            id: \"fuel-level\",\n                            name: \"fuel-level\",\n                            type: \"number\",\n                            defaultValue: training === null || training === void 0 ? void 0 : training.FuelLevel,\n                            className: \"w-full\",\n                            placeholder: \"Fuel level\",\n                            onChange: lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()(function(e) {\n                                setTraining({\n                                    ...training,\n                                    FuelLevel: e.target.value\n                                });\n                            }, 600)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1424,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1421,\n                        columnNumber: 25\n                    }, undefined),\n                    displayField(\"CrewTraining_StartTime\") || displayField(\"CrewTraining_FinishTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Training duration\",\n                        className: \"mb-1\",\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1442,\n                        columnNumber: 29\n                    }, undefined),\n                    displayField(\"CrewTraining_StartTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Start time of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            time: startTime,\n                            handleTimeChange: handleStartTimeChange,\n                            timeID: \"startTime\",\n                            fieldName: \"Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1451,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1450,\n                        columnNumber: 25\n                    }, undefined),\n                    displayField(\"CrewTraining_FinishTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"End time of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            time: finishTime,\n                            handleTimeChange: handleFinishTimeChange,\n                            timeID: \"finishTime\",\n                            fieldName: \"Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1462,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1461,\n                        columnNumber: 25\n                    }, undefined),\n                    (!currentEvent || training) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        id: \"TrainingSummary\",\n                        placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                        handleEditorChange: handleEditorChange,\n                        content: content,\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1472,\n                        columnNumber: 25\n                    }, undefined),\n                    selectedMemberList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                children: \"Participant Signatures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1491,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                children: selectedMemberList.map((member, index)=>{\n                                    var _signatureMembers_find, _signatureMembers_find1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        title: member.label,\n                                        member: member.label,\n                                        memberId: member.value,\n                                        onSignatureChanged: onSignatureChanged,\n                                        signature: {\n                                            signatureData: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.SignatureData,\n                                            id: (_signatureMembers_find1 = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find1 === void 0 ? void 0 : _signatureMembers_find1.ID\n                                        },\n                                        locked: locked\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                        lineNumber: 1495,\n                                        columnNumber: 41\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1492,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end pb-4 pt-4 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                variant: \"back\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                                onClick: closeModal,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1524,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                variant: \"primary\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                                onClick: locked ? ()=>{} : handleSave,\n                                disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                                children: trainingID === 0 ? \"Save\" : \"Update\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1530,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1523,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1314,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-3/4 sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1547,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1546,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetBody, {\n                            children: training && trainingTypes.filter((type)=>{\n                                var _training_TrainingTypes;\n                                return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                            }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-md p-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium leading-6 mb-4\",\n                                            children: type.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                            lineNumber: 1562,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            dangerouslySetInnerHTML: {\n                                                __html: type.procedure\n                                            }\n                                        }, type.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                            lineNumber: 1565,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, type.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1559,\n                                    columnNumber: 37\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1549,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                    lineNumber: 1545,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1544,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n        lineNumber: 1310,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingEvent, \"IFIVVo0IGCE+7GWuQeWlOiL77ts=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation\n    ];\n});\n_c = CrewTrainingEvent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingEvent);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingEvent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvbG9nYm9vay9mb3Jtcy9jcmV3LXRyYWluaW5nLWV2ZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDeUQ7QUFDaEM7QUFDaUI7QUFBQTtBQUNDO0FBQ29EO0FBQ2pCO0FBQ1k7QUFHckM7QUFZbEI7QUFDWTtBQU1mO0FBQzBCO0FBQ2U7QUFDdEI7QUFFd0M7QUFFakQ7QUFDVDtBQUNnQztBQUN3QjtBQUM5QjtBQUNrQjtBQUNOO0FBQ0w7QUFDckI7QUFDQTtBQUNFO0FBT2pCO0FBQ007QUFFcEMsTUFBTWlELG9CQUFvQjtRQUFDLEVBQ3ZCQyxpQkFBaUIsQ0FBQyxFQUNsQkMsV0FBVyxDQUFDLEVBQ1pDLGdCQUFnQixLQUFLLEVBQ3JCQyxjQUFjLEtBQUssRUFDbkJDLFVBQVUsRUFDVkMsZ0JBQWdCLEVBQ2hCQyxVQUFVLEVBQ1ZDLFdBQVcsRUFDWEMsUUFBUSxFQUNSQyxhQUFhLEVBQ2JDLE9BQU8sRUFDUEMsTUFBTSxFQUNOQyxVQUFVLEtBQUssRUFDZkMsZ0JBQWdCLEVBZ0JuQjtRQTJ4QytDQywrQkFHV0EsZ0NBR0FBOztJQWh5Q3ZELE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHNUQsK0NBQVFBLENBQU07SUFDbEQsTUFBTSxDQUFDNkQsY0FBY0MsZ0JBQWdCLEdBQUc5RCwrQ0FBUUEsQ0FBTThDO0lBQ3RELE1BQU0sQ0FBQ2lCLFVBQVVDLFlBQVksR0FBR2hFLCtDQUFRQSxDQUFNLENBQUM7SUFDL0MsTUFBTSxDQUFDaUUsU0FBU0MsV0FBVyxHQUFHbEUsK0NBQVFBLENBQU07SUFDNUMsTUFBTSxDQUFDMEQsYUFBYVMsZUFBZSxHQUFHbkUsK0NBQVFBO0lBQzlDLE1BQU0sQ0FBQ29FLGNBQWNDLGdCQUFnQixHQUFHckUsK0NBQVFBLENBQzVDLElBQUlzRSxPQUFPQyxrQkFBa0I7SUFFakMsTUFBTUMsU0FBU3pELCtEQUFhQSxDQUFDO0lBQzdCLE1BQU0sQ0FBQzBELGVBQWVDLGlCQUFpQixHQUFHMUUsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDMkUsb0JBQW9CQyxzQkFBc0IsR0FBRzVFLCtDQUFRQSxDQUFDLEVBQUU7SUFDL0QsTUFBTSxDQUFDNkUsa0JBQWtCQyxvQkFBb0IsR0FBRzlFLCtDQUFRQSxDQUFDLEVBQUU7SUFDM0QsTUFBTSxDQUFDK0UsWUFBWUMsY0FBYyxHQUFHaEYsK0NBQVFBO0lBQzVDLE1BQU0sQ0FBQ2lGLGVBQWVDLGlCQUFpQixHQUFHbEYsK0NBQVFBLENBQU0sRUFBRTtJQUMxRCxNQUFNLENBQUNtRixtQkFBbUJDLHFCQUFxQixHQUFHcEYsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDcUYsc0JBQXNCQyx3QkFBd0IsR0FBR3RGLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQ3VGLHlCQUF5QkMsMkJBQTJCLEdBQUd4RiwrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUN5Rix5QkFBeUJDLDJCQUEyQixHQUFHMUYsK0NBQVFBLENBQUM7SUFDdkUsTUFBTSxDQUFDMkYsc0JBQXNCQyx3QkFBd0IsR0FBRzVGLCtDQUFRQSxDQUFNLEVBQUU7SUFDeEUsTUFBTSxDQUFDNkYsb0JBQW9CQyxzQkFBc0IsR0FBRzlGLCtDQUFRQSxDQUFNLEVBQUU7SUFDcEUsTUFBTSxDQUFDK0YsZ0JBQWdCQyxrQkFBa0IsR0FBR2hHLCtDQUFRQSxDQUFNO0lBQzFELE1BQU0sQ0FBQ2lHLGNBQWNDLGdCQUFnQixHQUFHbEcsK0NBQVFBLENBQU07SUFDdEQsTUFBTSxDQUFDbUcscUJBQXFCQyx1QkFBdUIsR0FBR3BHLCtDQUFRQSxDQUFNO0lBQ3BFLE1BQU0sQ0FBQ3FHLGtCQUFrQkMsb0JBQW9CLEdBQUd0RywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUN1RyxZQUFZQyxjQUFjLEdBQUd4RywrQ0FBUUEsQ0FBQztRQUN6Q3lHLGVBQWU7UUFDZkMsV0FBVztRQUNYQyxVQUFVO1FBQ1ZyQyxNQUFNO0lBQ1Y7SUFDQSxNQUFNLENBQUNzQyxXQUFXQyxhQUFhLEdBQUc3RywrQ0FBUUEsQ0FBTUosNENBQUtBLEdBQUdrSCxNQUFNLENBQUM7SUFDL0QsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdoSCwrQ0FBUUEsQ0FBTUosNENBQUtBLEdBQUdrSCxNQUFNLENBQUM7SUFDakUsTUFBTUcsa0JBQWtCO1FBQ3BCN0Q7V0FDSThELE1BQU1DLE9BQU8sQ0FBQ2hFLGVBQ1pBLFlBQVlpRSxHQUFHLENBQUMsQ0FBQ0MsSUFBV0EsRUFBRUMsWUFBWSxJQUMxQyxFQUFFO0tBQ1g7SUFDRCxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUd4SCwrQ0FBUUEsQ0FBTTtRQUN4RHlILFVBQVU7UUFDVkMsV0FBVztJQUNmO0lBRUEsTUFBTUMsb0JBQW9CLElBQUkvRix5RUFBaUJBO0lBQy9DLE1BQU1nRyxnQ0FBZ0MsSUFBSS9GLHFGQUE2QkE7SUFDdkUsTUFBTWdHLGlCQUFpQixJQUFJL0Ysc0VBQWNBO0lBQ3pDLE1BQU1nRywwQkFBMEIsSUFBSS9GLCtFQUF1QkE7SUFDM0QsTUFBTWdHLHVCQUF1QixJQUFJL0YsNEVBQW9CQTtJQUVyRCxJQUFJLENBQUN3QixTQUFTO1FBQ1ZqQyxtRUFBZ0JBLENBQUMyRDtJQUNyQjtJQUVBLE1BQU04QyxvQkFBb0IsQ0FBQ0M7UUFDdkIsTUFBTUMsUUFBUSxJQUFJNUQsS0FBSzJELEVBQUVFLElBQUksRUFBRTVELGtCQUFrQjtRQUNqREYsZ0JBQWdCNkQ7UUFDaEIsTUFBTUUsZUFBZTtZQUNqQkMsSUFBSTFFO1lBQ0pXLE1BQU0xRSw0Q0FBS0EsQ0FBQ3FJLEVBQUVFLElBQUksRUFBRXJCLE1BQU0sQ0FBQztZQUMzQndCLFNBQVNMLEVBQUVNLE9BQU8sQ0FBQ0MsS0FBSyxDQUFDcEIsR0FBRyxDQUFDLENBQUNDLElBQVdBLEVBQUVvQixFQUFFO1lBQzdDL0IsV0FBV3VCLEVBQUVTLE9BQU8sQ0FBQ0QsRUFBRTtZQUN2QkUsaUJBQWlCVixFQUFFVSxlQUFlO1lBQ2xDbEMsZUFBZXdCLEVBQUVoRCxhQUFhLENBQUN1RCxLQUFLLENBQUNwQixHQUFHLENBQUMsQ0FBQ2EsSUFBV0EsRUFBRVEsRUFBRTtZQUN6RDlCLFVBQVU5RDtZQUNWK0YsV0FBV1gsRUFBRVksU0FBUyxJQUFJO1lBQzFCQyxlQUFlYixFQUFFYyxhQUFhO1lBQzlCQyxXQUFXZixFQUFFckIsU0FBUztZQUN0QnFDLFlBQVloQixFQUFFbEIsVUFBVTtZQUN4Qm1DLEtBQUtqQixFQUFFa0IsR0FBRztZQUNWQyxNQUFNbkIsRUFBRW9CLElBQUk7UUFDaEI7UUFDQW5GLFdBQVcrRCxFQUFFVSxlQUFlO1FBQzVCOUIsYUFBYW9CLEVBQUVyQixTQUFTO1FBQ3hCSSxjQUFjaUIsRUFBRWxCLFVBQVU7UUFDMUI1QyxlQUFlOEQ7UUFDZmpFLFlBQVlvRTtRQUNaLElBQUksQ0FBQ0gsRUFBRWMsYUFBYSxHQUFHLEdBQUc7WUFDdEJ2QixtQkFBbUI7Z0JBQ2ZDLFVBQVVRLEVBQUVxQixXQUFXLENBQUNILEdBQUc7Z0JBQzNCekIsV0FBV08sRUFBRXFCLFdBQVcsQ0FBQ0QsSUFBSTtZQUNqQztRQUNKLE9BQU87WUFDSDdCLG1CQUFtQjtnQkFDZkMsVUFBVVEsRUFBRWtCLEdBQUc7Z0JBQ2Z6QixXQUFXTyxFQUFFb0IsSUFBSTtZQUNyQjtRQUNKO1FBRUEsTUFBTWQsVUFDRk4sRUFBRU0sT0FBTyxDQUFDQyxLQUFLLENBQUNwQixHQUFHLENBQUMsQ0FBQ0M7Z0JBQ1BBLGNBQXFCQTttQkFERjtnQkFDN0JrQyxPQUFPLEdBQXdCbEMsT0FBckJBLENBQUFBLGVBQUFBLEVBQUVtQyxTQUFTLGNBQVhuQywwQkFBQUEsZUFBZSxJQUFHLEtBQW1CLE9BQWhCQSxDQUFBQSxhQUFBQSxFQUFFb0MsT0FBTyxjQUFUcEMsd0JBQUFBLGFBQWE7Z0JBQzVDcUMsT0FBT3JDLEVBQUVvQixFQUFFO1lBQ2Y7UUFBQSxNQUFPLEVBQUU7UUFDYjdELHNCQUFzQjJEO1FBQ3RCLE1BQU1vQixhQUFhMUIsRUFBRTBCLFVBQVUsQ0FBQ25CLEtBQUssQ0FBQ3BCLEdBQUcsQ0FBQyxDQUFDd0MsSUFBWTtnQkFDbkRDLFVBQVVELEVBQUVFLE1BQU0sQ0FBQ3JCLEVBQUU7Z0JBQ3JCc0IsZUFBZUgsRUFBRUksYUFBYTtnQkFDOUIzQixJQUFJdUIsRUFBRW5CLEVBQUU7WUFDWjtRQUNBM0Qsb0JBQW9CNkU7SUFDeEI7SUFFQSxNQUFNTSxxQkFBcUIsQ0FBQ0M7UUFDeEJoRyxXQUFXZ0c7SUFDZjtJQUVBLDRDQUE0QztJQUM1Qyw0RUFBNEU7SUFDNUUsOEJBQThCO0lBQzlCLFlBQVk7SUFDWiw4QkFBOEI7SUFDOUIsOEJBQThCO0lBQzlCLGFBQWE7SUFDYixZQUFZO0lBQ1osc0NBQXNDO0lBQ3RDLGdDQUFnQztJQUNoQyxhQUFhO0lBQ2IsbURBQW1EO0lBQ25ELGdDQUFnQztJQUNoQyxtQ0FBbUM7SUFDbkMsZUFBZTtJQUNmLFFBQVE7SUFDUixnQ0FBZ0M7SUFDaEMsSUFBSTtJQUVKbkssZ0RBQVNBLENBQUM7UUFDTixJQUFJdUQsU0FBUztZQUNULE1BQU02RyxnQkFBZ0I3RyxvQkFBQUEsOEJBQUFBLFFBQVM4RyxNQUFNLENBQ2pDLENBQUNDLFNBQWdCLENBQUNBLE9BQU9DLFFBQVE7WUFFckMsTUFBTUMsZ0JBQWdCO2dCQUNsQjtvQkFDSWhCLE9BQU87b0JBQ1BHLE9BQU87Z0JBQ1g7Z0JBQ0E7b0JBQ0lILE9BQU87b0JBQ1BHLE9BQU87Z0JBQ1g7bUJBQ0dTLGNBQWMvQyxHQUFHLENBQUMsQ0FBQ2lELFNBQWlCO3dCQUNuQ1gsT0FBT1csT0FBTzVCLEVBQUU7d0JBQ2hCYyxPQUFPYyxPQUFPRyxLQUFLO29CQUN2QjthQUNIO1lBQ0R4RixjQUFjdUY7UUFDbEI7SUFDSixHQUFHO1FBQUNqSDtLQUFRO0lBRVoscURBQXFEO0lBQ3JELHdDQUF3QztJQUN4QyxtREFBbUQ7SUFDbkQsdURBQXVEO0lBQ3ZELHNFQUFzRTtJQUN0RSxZQUFZO0lBQ1osU0FBUztJQUNULGlDQUFpQztJQUNqQyxxREFBcUQ7SUFDckQsU0FBUztJQUNULEtBQUs7SUFFTCxNQUFNLENBQ0ZtSCwrQkFDQSxFQUFFQyxTQUFTQyxvQ0FBb0MsRUFBRSxDQUNwRCxHQUFHdkosNERBQVdBLENBQUNmLDhFQUF1QkEsRUFBRTtRQUNyQ3VLLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTRSxxQkFBcUI7WUFDM0MsSUFBSUQsS0FBS3JDLEVBQUUsR0FBRyxHQUFHO2dCQUNiLElBQUk5QyxxQkFBcUJxRixNQUFNLEdBQUcsR0FBRztvQkFDakMsTUFBTUMsa0JBQWtCdEYscUJBQXFCeUIsR0FBRyxDQUM1QyxDQUFDOEQ7NEJBTWdCckY7d0JBTGIsT0FBTzs0QkFDSHNGLFFBQVFELGVBQWVDLE1BQU0sR0FBRyxPQUFPOzRCQUN2Q0MsbUJBQW1CTixLQUFLckMsRUFBRTs0QkFDMUI0Qyw0QkFDSUgsZUFBZUksT0FBTzs0QkFDMUJDLE9BQU8sR0FBRTFGLDJCQUFBQSxtQkFBbUIyRixJQUFJLENBQzVCLENBQUNELFVBQ0dBLFFBQVFELE9BQU8sSUFDZkosZUFBZUksT0FBTyxlQUhyQnpGLCtDQUFBQSx5QkFJTjBGLE9BQU87d0JBQ2Q7b0JBQ0o7b0JBRUpOLGdCQUFnQlEsT0FBTyxDQUFDLENBQUNQO3dCQUNyQlEsbUNBQW1DOzRCQUMvQkMsV0FBVztnQ0FDUEMsT0FBT1Y7NEJBQ1g7d0JBQ0o7b0JBQ0o7Z0JBQ0o7Z0JBQ0F0SCxjQUFja0gsS0FBS3JDLEVBQUU7Z0JBQ3JCb0Q7Z0JBQ0FDLGlCQUFpQmhCLEtBQUtyQyxFQUFFO2dCQUN4QndCLG1CQUFtQmEsS0FBS25DLGVBQWU7Z0JBQ3ZDb0QsZ0JBQWdCO29CQUNaSixXQUFXO3dCQUNQQyxPQUFPOzRCQUNIbkQsSUFBSSxFQUFDNUUseUJBQUFBLG1DQUFBQSxhQUFjNEUsRUFBRTs0QkFDckJ1RCxlQUFlOzRCQUNmQyxnQkFBZ0JuQixLQUFLckMsRUFBRTt3QkFDM0I7b0JBQ0o7Z0JBQ0o7Z0JBQ0F6RjtZQUNKLE9BQU87Z0JBQ0hrSixRQUFRQyxLQUFLLENBQUMsdUNBQXVDdEI7WUFDekQ7UUFDSjtRQUNBdUIsU0FBUyxDQUFDRDtZQUNORCxRQUFRQyxLQUFLLENBQUMsdUNBQXVDQTtRQUN6RDtJQUNKO0lBQ0EsTUFBTSxDQUNGRSwrQkFDQSxFQUFFM0IsU0FBUzRCLG9DQUFvQyxFQUFFLENBQ3BELEdBQUdsTCw0REFBV0EsQ0FBQ2QsOEVBQXVCQSxFQUFFO1FBQ3JDc0ssYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVMwQixxQkFBcUI7WUFDM0MsSUFBSXpCLEtBQUtyQyxFQUFFLEdBQUcsR0FBRztnQkFDYm9EO2dCQUNBQyxpQkFBaUJuSTtnQkFDakJzRyxtQkFBbUJhLEtBQUtuQyxlQUFlO1lBQzNDLE9BQU87Z0JBQ0h1RCxRQUFRQyxLQUFLLENBQUMsdUNBQXVDdEI7WUFDekQ7UUFDSjtRQUNBdUIsU0FBUyxDQUFDRDtZQUNORCxRQUFRQyxLQUFLLENBQUMsdUNBQXVDQTtRQUN6RDtJQUNKO0lBQ0EsTUFBTSxDQUNGSywyQkFDQSxFQUFFOUIsU0FBUytCLGdDQUFnQyxFQUFFLENBQ2hELEdBQUdwTCw2REFBWUEsQ0FBQ0gsa0ZBQTZCQSxFQUFFO1FBQzVDd0wsYUFBYTtRQUNiOUIsYUFBYSxDQUFDQztZQUNWLE9BQU9BLFNBQVMyQix5QkFBeUIsQ0FBQzFCLElBQUk7UUFDbEQ7UUFDQXNCLFNBQVMsQ0FBQ0Q7WUFDTkQsUUFBUUMsS0FBSyxDQUFDLDJDQUEyQ0E7WUFDekQsT0FBTztRQUNYO0lBQ0o7SUFDQSxNQUFNUSxxQ0FBcUM7WUFDdkNoQiw2RUFBaUIsQ0FBQyxHQUNsQmY7UUFFQSxJQUFJcEgsU0FBUztZQUNULE1BQU1vSixVQUFVLE1BQU05RSx3QkFBd0IrRSxNQUFNO1lBQ3BELE1BQU0vQixPQUFPOEIsUUFBUXhDLE1BQU0sQ0FDdkIsQ0FBQzBDLE9BQ0dBLEtBQUtDLFFBQVEsS0FBS3BCLFVBQVV2QixNQUFNLENBQUMyQyxRQUFRLENBQUNDLEVBQUUsSUFDOUNGLEtBQUtHLFFBQVEsS0FBS3RCLFVBQVV2QixNQUFNLENBQUM2QyxRQUFRLENBQUNELEVBQUUsSUFDOUNGLEtBQUtJLGNBQWMsS0FBS3ZCLFVBQVV2QixNQUFNLENBQUM4QyxjQUFjLENBQUNGLEVBQUU7WUFFbEVwQyxZQUFZRTtRQUNoQixPQUFPO1lBQ0gsTUFBTSxFQUFFQSxJQUFJLEVBQUUsR0FBUSxNQUFNMEIsMEJBQTBCO2dCQUNsRGIsV0FBV0E7WUFDZjtZQUNBZixZQUFZRSxLQUFLMEIseUJBQXlCO1FBQzlDO0lBQ0o7SUFFQSxNQUFNLENBQ0ZXLGtDQUNBLEVBQUV6QyxTQUFTMEMsK0JBQStCLEVBQUUsQ0FDL0MsR0FBR2hNLDREQUFXQSxDQUFDWCxrRkFBMkJBLEVBQUU7UUFDekNtSyxhQUFhLENBQUNDLFlBQW1CO1FBQ2pDdUIsU0FBUyxDQUFDRDtZQUNORCxRQUFRQyxLQUFLLENBQUMsa0NBQWtDQTtRQUNwRDtJQUNKO0lBQ0EsTUFBTSxDQUNGa0Isa0NBQ0EsRUFBRTNDLFNBQVM0QywrQkFBK0IsRUFBRSxDQUMvQyxHQUFHbE0sNERBQVdBLENBQUNWLGtGQUEyQkEsRUFBRTtRQUN6Q2tLLGFBQWEsQ0FBQ0MsWUFBbUI7UUFDakN1QixTQUFTLENBQUNEO1lBQ05ELFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ3BEO0lBQ0o7SUFDQSxNQUFNTiw0QkFBNEI7UUFDOUIsTUFBTTBCLHNCQUEyQixFQUFFO1FBQ25DLE1BQU1OLFdBQVdsSixTQUFTNEMsUUFBUTtRQUNsQzVDLFNBQVMwQyxhQUFhLENBQUNnRixPQUFPLENBQUMsQ0FBQ3hEO1lBQzVCLE1BQU11RixlQUFldkksY0FBY3VHLElBQUksQ0FBQyxDQUFDaUMsS0FBWUEsR0FBR2hGLEVBQUUsS0FBS1I7WUFFL0QsSUFBSSxDQUFDbkkscURBQU9BLENBQUMwTixpQkFBaUJBLGFBQWFFLFdBQVcsR0FBRyxHQUFHO2dCQUN4RCxNQUFNUixpQkFBaUJqRjtnQkFDdkIsTUFBTTBGLGFBQWEvTiw0Q0FBS0EsQ0FBQ21FLFNBQVNPLElBQUksRUFBRXNKLEdBQUcsQ0FDdkNKLGFBQWFFLFdBQVcsRUFDeEI7Z0JBRUozSixTQUFTdUUsT0FBTyxDQUFDbUQsT0FBTyxDQUFDLENBQUNwRTtvQkFDdEIsTUFBTTBGLFdBQVcxRjtvQkFDakJrRyxvQkFBb0JNLElBQUksQ0FBQzt3QkFDckJDLFNBQVNILFdBQVc3RyxNQUFNLENBQUM7d0JBQzNCaUcsVUFBVUE7d0JBQ1ZFLFVBQVVBO3dCQUNWQyxnQkFBZ0JBO29CQUNwQjtnQkFDSjtZQUNKO1FBQ0o7UUFDQSxJQUFJYSw0QkFBaUMsRUFBRTtRQUN2QyxJQUFJLENBQUNqTyxxREFBT0EsQ0FBQ3lOLHNCQUFzQjtZQUMvQixNQUFNUyxRQUFRQyxHQUFHLENBQ2JWLG9CQUFvQm5HLEdBQUcsQ0FBQyxPQUFPMEY7Z0JBQzNCLE1BQU1uQixZQUFZO29CQUNkdkIsUUFBUTt3QkFDSjJDLFVBQVU7NEJBQ05DLElBQUlGLEtBQUtDLFFBQVE7d0JBQ3JCO3dCQUNBRSxVQUFVOzRCQUNORCxJQUFJRixLQUFLRyxRQUFRO3dCQUNyQjt3QkFDQUMsZ0JBQWdCOzRCQUNaRixJQUFJRixLQUFLSSxjQUFjO3dCQUMzQjtvQkFDSjtnQkFDSjtnQkFDQSxNQUFNdEMsY0FBYyxDQUFDQzt3QkFHVEE7b0JBRlJrRCwwQkFBMEJGLElBQUksQ0FBQzt3QkFDM0IsR0FBR2YsSUFBSTt3QkFDUHJFLElBQUlvQyxDQUFBQSxlQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVwQyxFQUFFLGNBQVpvQywwQkFBQUEsZUFBZ0I7b0JBQ3hCO2dCQUNKO2dCQUVBLE1BQU04QixtQ0FDRmhCLFdBQ0FmO1lBRVI7UUFFUjtRQUVBLElBQUksQ0FBQzlLLHFEQUFPQSxDQUFDaU8sNEJBQTRCO1lBQ3JDLE1BQU1DLFFBQVFDLEdBQUcsQ0FDYi9HLE1BQU1nSCxJQUFJLENBQUNILDJCQUEyQjNHLEdBQUcsQ0FBQyxPQUFPMEY7Z0JBQzdDLE1BQU1uQixZQUFZO29CQUNkQSxXQUFXO3dCQUFFQyxPQUFPa0I7b0JBQUs7Z0JBQzdCO2dCQUNBLElBQUlBLEtBQUtyRSxFQUFFLEtBQUssR0FBRztvQkFDZixJQUFJakYsU0FBUzt3QkFDVCxtQ0FBbUM7d0JBQ25DLE1BQU1zRSx3QkFBd0JxRyxJQUFJLENBQUM7NEJBQy9CLEdBQUdyQixJQUFJOzRCQUNQckUsSUFBSXhHLGlGQUFnQkE7d0JBQ3hCO29CQUNKLE9BQU87d0JBQ0gsTUFBTWtMLGlDQUFpQ3hCO29CQUMzQztnQkFDSixPQUFPO29CQUNILElBQUluSSxTQUFTO3dCQUNULG1DQUFtQzt3QkFDbkMsTUFBTXNFLHdCQUF3QnFHLElBQUksQ0FBQ3JCO29CQUN2QyxPQUFPO3dCQUNILE1BQU1PLGlDQUFpQzFCO29CQUMzQztnQkFDSjtZQUNKO1FBRVI7SUFDSjtJQUVBLE1BQU0sQ0FBQ0ksZ0JBQWdCLEdBQUczSyw0REFBV0EsQ0FBQ1Isc0VBQWVBLEVBQUU7UUFDbkRnSyxhQUFhLENBQUNDO1lBQ1Z1RCxnQkFBZ0J2Syx5QkFBQUEsbUNBQUFBLGFBQWM0RSxFQUFFO1lBQ2hDeEYsaUJBQWlCO2dCQUNid0YsSUFBSTt1QkFBSXZGLFdBQVdrRSxHQUFHLENBQUMsQ0FBQ2lILE9BQWNBLEtBQUs1RixFQUFFO29CQUFHMUYsWUFBWTBGLEVBQUU7aUJBQUM7WUFDbkU7UUFDSjtRQUNBMkQsU0FBUyxDQUFDRDtZQUNORCxRQUFRQyxLQUFLLENBQUMsNkJBQTZCQTtRQUMvQztJQUNKO0lBQ0EsTUFBTSxDQUFDbUMsZ0JBQWdCLEdBQUdsTiw0REFBV0EsQ0FBQ1Qsc0VBQWVBLEVBQUU7UUFDbkRpSyxhQUFhLENBQUNDO1lBQ1YsTUFBTUMsT0FBT0QsU0FBU3lELGVBQWU7WUFDckN4SyxnQkFBZ0JnSDtZQUNoQnlEO1FBQ0o7UUFDQW5DLFNBQVMsQ0FBQ0Q7WUFDTkQsUUFBUUMsS0FBSyxDQUFDLDZCQUE2QkE7UUFDL0M7SUFDSjtJQUVBLE1BQU1vQyxlQUFlO1lBTUp4SyxtQkFHTUE7UUFSbkIsTUFBTTZILFFBQVE7WUFDVm5ELElBQUk5RTtZQUNKd0UsTUFBTXBFLFNBQVNPLElBQUksR0FDYjFFLDRDQUFLQSxDQUFDNkQsa0JBQWtCcUQsTUFBTSxDQUFDLGdCQUMvQjtZQUNOeUIsT0FBTyxHQUFFeEUsb0JBQUFBLFNBQVN1RSxPQUFPLGNBQWhCdkUsd0NBQUFBLGtCQUFrQnlLLElBQUksQ0FBQztZQUNoQ0MsV0FBVzFLLFNBQVMyQyxTQUFTO1lBQzdCaUMsaUJBQWlCMUU7WUFDakJnQixhQUFhLEdBQUVsQiwwQkFBQUEsU0FBUzBDLGFBQWEsY0FBdEIxQyw4Q0FBQUEsd0JBQXdCeUssSUFBSSxDQUFDO1lBQzVDdkIsUUFBUSxFQUFFbEoscUJBQUFBLCtCQUFBQSxTQUFVNEMsUUFBUTtZQUM1QitILHNCQUFzQjNLLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVTRDLFFBQVEsSUFDbEM1QyxTQUFTNEMsUUFBUSxLQUFLLFdBQ3RCNUMsU0FBUzRDLFFBQVEsS0FBSyxZQUNsQjVDLFNBQVM0QyxRQUFRLEdBQ2pCLFdBQ0o7WUFDTmtDLFdBQVcsR0FBc0IsT0FBbkI5RSxTQUFTNkUsU0FBUztZQUNoQ0csZUFBZWhGLFNBQVMrRSxhQUFhO1lBQ3JDbEMsV0FBV0E7WUFDWEcsWUFBWUE7WUFDWm9DLEtBQUssR0FBZ0IsT0FBYnBGLFNBQVNtRixHQUFHO1lBQ3BCRyxNQUFNLEdBQWlCLE9BQWR0RixTQUFTcUYsSUFBSTtRQUMxQjtRQUNBLElBQUl6RixlQUFlLEdBQUc7WUFDbEIsSUFBSUgsU0FBUztnQkFDVCxnQ0FBZ0M7Z0JBQ2hDLE1BQU1zSCxPQUFPLE1BQU0vQyxxQkFBcUJvRyxJQUFJLENBQUM7b0JBQ3pDLEdBQUd2QyxLQUFLO29CQUNSbkQsSUFBSXhHLGlGQUFnQkE7Z0JBQ3hCO2dCQUVBMkIsY0FBY2tILEtBQUtyQyxFQUFFO2dCQUNyQm9EO2dCQUNBQyxpQkFBaUJoQixLQUFLckMsRUFBRTtnQkFDeEJ3QixtQkFBbUJhLEtBQUtuQyxlQUFlO2dCQUN2QyxrQkFBa0I7Z0JBQ2xCLE1BQU1kLGVBQWVzRyxJQUFJLENBQUM7b0JBQ3RCMUYsSUFBSSxFQUFDNUUseUJBQUFBLG1DQUFBQSxhQUFjNEUsRUFBRTtvQkFDckJ1RCxlQUFlO29CQUNmQyxnQkFBZ0JuQixLQUFLckMsRUFBRTtnQkFDM0I7Z0JBRUEsTUFBTTJGLGdCQUFnQnZLLHlCQUFBQSxtQ0FBQUEsYUFBYzRFLEVBQUU7Z0JBQ3RDeEYsaUJBQWlCO29CQUNid0YsSUFBSTsyQkFDR3ZGLFdBQVdrRSxHQUFHLENBQUMsQ0FBQ2lILE9BQWNBLEtBQUs1RixFQUFFO3dCQUN4QzFGLFlBQVkwRixFQUFFO3FCQUNqQjtnQkFDTDtnQkFDQXpGO1lBQ0osT0FBTztnQkFDSCxNQUFNeUgsOEJBQThCO29CQUNoQ2tCLFdBQVc7d0JBQ1BDLE9BQU9BO29CQUNYO2dCQUNKO1lBQ0o7UUFDSixPQUFPO1lBQ0gsSUFBSXBJLFNBQVM7Z0JBQ1QsZ0NBQWdDO2dCQUNoQyxNQUFNc0gsT0FBTyxNQUFNL0MscUJBQXFCb0csSUFBSSxDQUFDdkM7Z0JBQzdDQztnQkFDQUMsaUJBQWlCbkk7Z0JBQ2pCc0csbUJBQW1CYSxLQUFLbkMsZUFBZTtZQUMzQyxPQUFPO2dCQUNILE1BQU0wRCw4QkFBOEI7b0JBQ2hDVixXQUFXO3dCQUNQQyxPQUFPQTtvQkFDWDtnQkFDSjtZQUNKO1FBQ0o7SUFDSjtJQUNBLE1BQU0rQyxhQUFhO1FBQ2YsSUFBSUMsWUFBWTtRQUNoQixJQUFJQyxTQUFTO1lBQ1RwSSxlQUFlO1lBQ2ZDLFdBQVc7WUFDWEMsVUFBVTtZQUNWckMsTUFBTTtRQUNWO1FBQ0FrQyxjQUFjcUk7UUFDZCx3REFBd0Q7UUFDeEQsSUFBSSxDQUFDOUssU0FBUzBDLGFBQWEsSUFBSTNHLHFEQUFPQSxDQUFDaUUsU0FBUzBDLGFBQWEsR0FBRztZQUM1RG1JLFlBQVk7WUFDWkMsT0FBT3BJLGFBQWEsR0FBRztZQUV2QixnREFBZ0Q7WUFDaERELGNBQWMsQ0FBQ3NJLGFBQWdCO29CQUMzQixHQUFHQSxVQUFVO29CQUNickksZUFBZTtnQkFDbkI7UUFDSixPQUFPO1lBQ0gscURBQXFEO1lBQ3JERCxjQUFjLENBQUNzSSxhQUFnQjtvQkFDM0IsR0FBR0EsVUFBVTtvQkFDYnJJLGVBQWU7Z0JBQ25CO1FBQ0o7UUFDQSxJQUFJLENBQUUxQyxDQUFBQSxTQUFTMkMsU0FBUyxJQUFJM0MsU0FBUzJDLFNBQVMsR0FBRyxJQUFJO1lBQ2pEa0ksWUFBWTtZQUNaQyxPQUFPbkksU0FBUyxHQUFHO1FBQ3ZCO1FBQ0EsSUFDSSxDQUFDM0MsU0FBUzRDLFFBQVEsSUFDbEIsQ0FBRTVDLENBQUFBLFNBQVNnTCxrQkFBa0IsSUFBSWhMLFNBQVNnTCxrQkFBa0IsSUFBSSxJQUNsRTtZQUNFSCxZQUFZO1lBQ1pDLE9BQU9sSSxRQUFRLEdBQUc7UUFDdEI7UUFFQSxJQUFJLE9BQU81QyxTQUFTTyxJQUFJLEtBQUssYUFBYTtZQUN0Q1AsU0FBU08sSUFBSSxHQUFHMUUsNENBQUtBLENBQUM2RCxrQkFBa0JxRCxNQUFNLENBQUM7UUFDbkQ7UUFFQSxJQUFJL0MsU0FBU08sSUFBSSxLQUFLLFFBQVEsQ0FBQzFFLDRDQUFLQSxDQUFDbUUsU0FBU08sSUFBSSxFQUFFMEssT0FBTyxJQUFJO1lBQzNESixZQUFZO1lBQ1pDLE9BQU92SyxJQUFJLEdBQUc7UUFDbEI7UUFDQSxJQUFJc0ssV0FBVztZQUNYbEssaUJBQWlCO1lBQ2pCOEIsY0FBY3FJO1lBQ2Q7UUFDSjtRQUNBLElBQUloTCxjQUFjO1lBQ2QsSUFBSUwsU0FBUztnQkFDVCxrQkFBa0I7Z0JBQ2xCLE1BQU1xRSxlQUFlc0csSUFBSSxDQUFDO29CQUN0QjFGLElBQUksQ0FBQzVFLGFBQWE0RSxFQUFFO29CQUNwQnVELGVBQWU7b0JBQ2ZpRCx1QkFBdUJsTSxZQUFZMEYsRUFBRTtnQkFDekM7Z0JBRUEsTUFBTTJGLGdCQUFnQnZLLHlCQUFBQSxtQ0FBQUEsYUFBYzRFLEVBQUU7Z0JBQ3RDeEYsaUJBQWlCO29CQUNid0YsSUFBSTsyQkFDR3ZGLFdBQVdrRSxHQUFHLENBQUMsQ0FBQ2lILE9BQWNBLEtBQUs1RixFQUFFO3dCQUN4QzFGLFlBQVkwRixFQUFFO3FCQUNqQjtnQkFDTDtZQUNKLE9BQU87Z0JBQ0hzRCxnQkFBZ0I7b0JBQ1pKLFdBQVc7d0JBQ1BDLE9BQU87NEJBQ0huRCxJQUFJLENBQUM1RSxhQUFhNEUsRUFBRTs0QkFDcEJ1RCxlQUFlOzRCQUNmaUQsdUJBQXVCbE0sWUFBWTBGLEVBQUU7d0JBQ3pDO29CQUNKO2dCQUNKO1lBQ0o7WUFDQThGO1lBQ0F2TDtRQUNKLE9BQU87WUFDSCxJQUFJUSxTQUFTO2dCQUNULGtCQUFrQjtnQkFDbEIsTUFBTTBMLGdCQUFnQixNQUFNckgsZUFBZXNHLElBQUksQ0FBQztvQkFDNUMxRixJQUFJeEcsaUZBQWdCQTtvQkFDcEIrSixlQUFlO29CQUNmaUQsdUJBQXVCbE0sWUFBWTBGLEVBQUU7Z0JBQ3pDO2dCQUNBM0UsZ0JBQWdCb0w7Z0JBQ2hCWDtZQUNKLE9BQU87Z0JBQ0hELGdCQUFnQjtvQkFDWjNDLFdBQVc7d0JBQ1BDLE9BQU87NEJBQ0hJLGVBQWU7NEJBQ2ZpRCx1QkFBdUJsTSxZQUFZMEYsRUFBRTt3QkFDekM7b0JBQ0o7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7SUFFQSxNQUFNcUQsbUJBQW1CLENBQUNxRDtRQUN0QnRLLGlCQUFpQm1HLE1BQU0sR0FBRyxNQUN0Qm5HLDZCQUFBQSx1Q0FBQUEsaUJBQWtCNEcsT0FBTyxDQUFDLENBQUMyRDtZQUN2QkMsc0JBQXNCRCxXQUFXRDtRQUNyQztJQUNSO0lBRUEsTUFBTUUsd0JBQXdCLE9BQzFCRCxXQUNBRDtRQUVBLElBQUkzTCxTQUFTO1lBQ1QsbUNBQW1DO1lBQ25DLE1BQU04TCxnQkFBZ0IsTUFBTTFILDhCQUE4QmlGLE1BQU07WUFDaEUsTUFBTS9CLE9BQU93RSxjQUFjbEYsTUFBTSxDQUM3QixDQUFDMEMsT0FDR0EsS0FBS0MsUUFBUSxLQUFLcUMsVUFBVXZGLFFBQVEsSUFDcENpRCxLQUFLMUIsaUJBQWlCLEtBQUsrRDtZQUVuQyxJQUFJckUsS0FBS0UsTUFBTSxHQUFHLEdBQUc7Z0JBQ2pCLHdDQUF3QztnQkFDeEMsTUFBTXBELDhCQUE4QnVHLElBQUksQ0FBQztvQkFDckMxRixJQUFJcUMsSUFBSSxDQUFDLEVBQUUsQ0FBQ3JDLEVBQUU7b0JBQ2RzRSxVQUFVcUMsVUFBVXZGLFFBQVE7b0JBQzVCRyxlQUFlb0YsVUFBVXJGLGFBQWE7b0JBQ3RDcUIsbUJBQW1CK0Q7Z0JBQ3ZCO1lBQ0osT0FBTztnQkFDSCx3Q0FBd0M7Z0JBQ3hDLE1BQU12SCw4QkFBOEJ1RyxJQUFJLENBQUM7b0JBQ3JDMUYsSUFBSXhHLGlGQUFnQkE7b0JBQ3BCOEssVUFBVXFDLFVBQVV2RixRQUFRO29CQUM1QkcsZUFBZW9GLFVBQVVyRixhQUFhO29CQUN0Q3FCLG1CQUFtQitEO2dCQUN2QjtZQUNKO1FBQ0osT0FBTztZQUNILE1BQU1JLGlDQUFpQztnQkFDbkM1RCxXQUFXO29CQUNQdkIsUUFBUTt3QkFDSjJDLFVBQVU7NEJBQUVDLElBQUlvQyxVQUFVdkYsUUFBUTt3QkFBQzt3QkFDbkN1QixtQkFBbUI7NEJBQUVvRSxJQUFJTDt3QkFBVztvQkFDeEM7Z0JBQ0o7WUFDSixHQUNLTSxJQUFJLENBQUMsQ0FBQzVFO2dCQUNILE1BQU1DLE9BQ0ZELFNBQVNDLElBQUksQ0FBQzRFLDZCQUE2QixDQUFDbEgsS0FBSztnQkFDckQsSUFBSXNDLEtBQUtFLE1BQU0sR0FBRyxHQUFHO29CQUNqQjJFLHNDQUFzQzt3QkFDbENoRSxXQUFXOzRCQUNQQyxPQUFPO2dDQUNIbkQsSUFBSXFDLElBQUksQ0FBQyxFQUFFLENBQUNyQyxFQUFFO2dDQUNkc0UsVUFBVXFDLFVBQVV2RixRQUFRO2dDQUM1QkcsZUFBZW9GLFVBQVVyRixhQUFhO2dDQUN0Q3FCLG1CQUFtQitEOzRCQUN2Qjt3QkFDSjtvQkFDSjtnQkFDSixPQUFPO29CQUNILElBQUlDLFVBQVVyRixhQUFhLEVBQUU7d0JBQ3pCNkYsc0NBQXNDOzRCQUNsQ2pFLFdBQVc7Z0NBQ1BDLE9BQU87b0NBQ0htQixVQUFVcUMsVUFBVXZGLFFBQVE7b0NBQzVCRyxlQUFlb0YsVUFBVXJGLGFBQWE7b0NBQ3RDcUIsbUJBQW1CK0Q7Z0NBQ3ZCOzRCQUNKO3dCQUNKO29CQUNKO2dCQUNKO1lBQ0osR0FDQ1UsS0FBSyxDQUFDLENBQUMxRDtnQkFDSkQsUUFBUUMsS0FBSyxDQUNULDZDQUNBQTtZQUVSO1FBQ1I7SUFDSjtJQUVBLE1BQU0sQ0FBQ29ELGlDQUFpQyxHQUFHbE8sNkRBQVlBLENBQ25ETCxtRkFBOEJBO0lBR2xDLE1BQU0sQ0FDRjJPLHVDQUNBLEVBQUVqRixTQUFTb0YsNENBQTRDLEVBQUUsQ0FDNUQsR0FBRzFPLDREQUFXQSxDQUFDWix1RkFBZ0NBLEVBQUU7UUFDOUNvSyxhQUFhLENBQUNDO1lBQ1YsTUFBTUMsT0FBT0QsU0FBU2tGLDhCQUE4QjtZQUNwRCxJQUFJakYsS0FBS3JDLEVBQUUsR0FBRyxHQUFHO1lBQ2IsbUJBQW1CO1lBQ25CLG9EQUFvRDtZQUNwRCxJQUFJO1lBQ1IsT0FBTztnQkFDSHlELFFBQVFDLEtBQUssQ0FDVCwrQ0FDQXRCO1lBRVI7UUFDSjtRQUNBdUIsU0FBUyxDQUFDRDtZQUNORCxRQUFRQyxLQUFLLENBQUMsK0NBQStDQTtRQUNqRTtJQUNKO0lBRUEsTUFBTSxDQUNGeUQsdUNBQ0EsRUFBRWxGLFNBQVNzRiw0Q0FBNEMsRUFBRSxDQUM1RCxHQUFHNU8sNERBQVdBLENBQUNiLHVGQUFnQ0EsRUFBRTtRQUM5Q3FLLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTb0YsOEJBQThCO1lBQ3BELElBQUluRixLQUFLckMsRUFBRSxHQUFHLEdBQUc7WUFDYixtQkFBbUI7WUFDbkIsb0RBQW9EO1lBQ3BELElBQUk7WUFDUixPQUFPO2dCQUNIeUQsUUFBUUMsS0FBSyxDQUNULCtDQUNBdEI7WUFFUjtRQUNKO1FBQ0F1QixTQUFTLENBQUNEO1lBQ05ELFFBQVFDLEtBQUssQ0FBQywrQ0FBK0NBO1FBQ2pFO0lBQ0o7SUFFQSxNQUFNK0QsMkJBQTJCLENBQUMvSDtRQUM5QjlELGdCQUFnQixJQUFJQyxLQUFLNkQsS0FBS2dJLFFBQVEsSUFBSTVMLGtCQUFrQjtRQUM1RFAsWUFBWTtZQUNSLEdBQUdELFFBQVE7WUFDWE8sTUFBTTFFLDRDQUFLQSxDQUFDdUksTUFBTXJCLE1BQU0sQ0FBQztRQUM3QjtJQUNKO0lBQ0EsTUFBTXNKLHNCQUFzQixDQUFDMUg7UUFDekIsSUFBSSxDQUFDQSxTQUFTLFFBQU8sc0NBQXNDO1FBRTNELHVFQUF1RTtRQUN2RSxNQUFNMkgsZUFBZW5KLE1BQU1DLE9BQU8sQ0FBQ3VCLFdBQzdCQSxRQUFRc0MsTUFBTSxHQUFHLElBQ2J0QyxPQUFPLENBQUMsRUFBRSxDQUFDZ0IsS0FBSyxHQUNoQixPQUNKaEIsUUFBUWdCLEtBQUs7UUFFbkIsSUFBSSxDQUFDMkcsY0FBYztZQUNmO1FBQ0o7UUFFQSxxRkFBcUY7UUFDckYsTUFBTUMsYUFBYSxJQUFJQyxJQUFJeE0sQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVdUUsT0FBTyxLQUFJLEVBQUU7UUFDbERnSSxXQUFXMUMsR0FBRyxDQUFDeUM7UUFDZixNQUFNOUgsVUFBVXJCLE1BQU1nSCxJQUFJLENBQUNvQztRQUUzQnRNLFlBQVk7WUFDUixHQUFHRCxRQUFRO1lBQ1gyQyxXQUFXMko7WUFDWC9ILFNBQVNDO1FBQ2I7UUFFQSw0REFBNEQ7UUFDNUQsTUFBTWlJLGdCQUFnQnRKLE1BQU1DLE9BQU8sQ0FBQ3VCLFdBQVdBLE9BQU8sQ0FBQyxFQUFFLEdBQUdBO1FBRTVELHdFQUF3RTtRQUN4RSxNQUFNK0gsMkJBQTJCOUwsbUJBQW1CK0wsSUFBSSxDQUNwRCxDQUFDNUcsU0FBZ0IsQ0FBQ0EsT0FBT0osS0FBSyxLQUFLLENBQUMyRztRQUd4QyxJQUFJLENBQUNJLDBCQUEwQjtZQUMzQjdMLHNCQUFzQjttQkFBSUQ7Z0JBQW9CNkw7YUFBYztZQUM1RDFMLG9CQUFvQjttQkFDYkQ7Z0JBQ0g7b0JBQ0lnRixVQUFVLENBQUN3RztvQkFDWHRHLGVBQWU7Z0JBQ25CO2FBQ0g7UUFDTDtJQUNKO0lBQ0EsTUFBTTRHLDJCQUEyQixDQUFDMUw7UUFDOUIsNENBQTRDO1FBQzVDLE1BQU0yTCxnQkFBZ0IsQ0FBQzlRLHFEQUFPQSxDQUFDbUYsaUJBQ3pCQSxjQUFjbUMsR0FBRyxDQUFDLENBQUMwRixPQUFjQSxLQUFLcEQsS0FBSyxJQUMzQyxFQUFFO1FBRVIxRixZQUFZO1lBQ1IsR0FBR0QsUUFBUTtZQUNYMEMsZUFBZW1LO1FBQ25CO1FBRUEsaURBQWlEO1FBQ2pELElBQUksQ0FBQzlRLHFEQUFPQSxDQUFDOFEsZ0JBQWdCO1lBQ3pCcEssY0FBYyxDQUFDc0ksYUFBZ0I7b0JBQzNCLEdBQUdBLFVBQVU7b0JBQ2JySSxlQUFlO2dCQUNuQjtRQUNKO0lBQ0o7SUFFQSxNQUFNb0sscUJBQXFCLENBQUN0STtRQUN4Qiw2QkFBNkI7UUFDN0IsTUFBTXVJLGVBQWU1SixNQUFNQyxPQUFPLENBQUNvQixXQUM3QkEsVUFDQTtZQUFDQTtTQUFRLENBQUM2QixNQUFNLENBQUMyRztRQUV2QixxREFBcUQ7UUFDckQsTUFBTXBILGFBQWE5RSxpQkFBaUJ1RixNQUFNLENBQUMsQ0FBQzBDLE9BQ3hDZ0UsYUFBYUosSUFBSSxDQUNiLENBQUNySixJQUFXQSxLQUFLQSxFQUFFcUMsS0FBSyxJQUFJLENBQUNyQyxFQUFFcUMsS0FBSyxLQUFLb0QsS0FBS2pELFFBQVE7UUFJOUQsK0JBQStCO1FBQy9CLE1BQU1tSCxlQUFlRixhQUNoQjFHLE1BQU0sQ0FBQyxDQUFDMEMsT0FBY0EsUUFBUUEsS0FBS3BELEtBQUssRUFDeEN0QyxHQUFHLENBQUMsQ0FBQzBGLE9BQWNBLEtBQUtwRCxLQUFLO1FBRWxDMUYsWUFBWTtZQUNSLEdBQUdELFFBQVE7WUFDWHVFLFNBQVMwSTtRQUViO1FBQ0FwTSxzQkFBc0JrTTtRQUN0QmhNLG9CQUFvQjZFO0lBQ3hCO0lBQ0EsTUFBTXNILHFCQUFxQixDQUN2QjdCLFdBQ0F0RixRQUNBb0g7UUFFQSxNQUFNQyxRQUFRdE0saUJBQWlCdU0sU0FBUyxDQUNwQyxDQUFDQyxTQUFXQSxPQUFPeEgsUUFBUSxLQUFLcUg7UUFFcEMsTUFBTUksaUJBQWlCO2VBQUl6TTtTQUFpQjtRQUM1QyxJQUFJdUssV0FBVztZQUNYLElBQUkrQixVQUFVLENBQUMsR0FBRztnQkFDZCxJQUFJL0IsVUFBVW1DLElBQUksT0FBTyxJQUFJO29CQUN6QkQsZUFBZUUsTUFBTSxDQUFDTCxPQUFPO2dCQUNqQyxPQUFPO29CQUNIRyxjQUFjLENBQUNILE1BQU0sQ0FBQ3BILGFBQWEsR0FBR3FGO2dCQUMxQztZQUNKLE9BQU87Z0JBQ0hrQyxlQUFlekQsSUFBSSxDQUFDO29CQUNoQmhFLFVBQVVxSDtvQkFDVm5ILGVBQWVxRjtnQkFDbkI7WUFDSjtRQUNKLE9BQU87WUFDSGtDLGVBQWVFLE1BQU0sQ0FBQ0wsT0FBTztRQUNqQztRQUNBck0sb0JBQW9Cd007SUFDeEI7SUFFQSxJQUFJLENBQUM5TixTQUFTO1FBQ1ZsQyxzRUFBbUJBLENBQUNzQixnQkFBZ0JvQjtJQUN4QztJQUVBLE1BQU15Tiw2QkFBNkIsQ0FBQ3BIO1FBQ2hDckcsWUFBWTtZQUNSLEdBQUdELFFBQVE7WUFDWDRDLFVBQVUwRCxPQUFPWCxLQUFLO1FBQzFCO0lBQ0o7SUFFQSxNQUFNMEUsa0JBQWtCLE9BQU8zRjtRQUMzQixJQUFJakYsU0FBUztZQUNULGVBQWU7WUFDZixNQUFNa08sUUFBUSxNQUFNN0osZUFBZThKLE9BQU8sQ0FBQ2xKO1lBQzNDLElBQUlpSixPQUFPO2dCQUNQOU4sY0FBYzhOLE1BQU16RixjQUFjO1lBQ3RDO1FBQ0osT0FBTztZQUNIMkYsYUFBYTtnQkFDVGpHLFdBQVc7b0JBQ1BsRCxJQUFJQTtnQkFDUjtZQUNKO1FBQ0o7SUFDSjtJQUVBLE1BQU0sQ0FBQ21KLGFBQWEsR0FBR3ZRLDZEQUFZQSxDQUFDSixpRUFBWUEsRUFBRTtRQUM5Q3lMLGFBQWE7UUFDYjlCLGFBQWEsQ0FBQ0M7WUFDVixNQUFNNkcsUUFBUTdHLFNBQVNnSCxnQkFBZ0I7WUFDdkMsSUFBSUgsT0FBTztnQkFDUDlOLGNBQWM4TixNQUFNSSxZQUFZLENBQUNySixFQUFFO1lBQ3ZDO1FBQ0o7UUFDQTJELFNBQVMsQ0FBQ0Q7WUFDTkQsUUFBUUMsS0FBSyxDQUFDLCtCQUErQkE7UUFDakQ7SUFDSjtJQUNBLE1BQU0sQ0FBQzRGLHlCQUF5QixHQUFHMVEsNkRBQVlBLENBQUNGLDJFQUFzQkEsRUFBRTtRQUNwRXVMLGFBQWE7UUFDYjlCLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTbUgsc0JBQXNCO1lBQzVDLElBQUlsSCxNQUFNO2dCQUNOOUMsa0JBQWtCOEM7WUFDdEI7UUFDSjtRQUNBc0IsU0FBUyxDQUFDRDtZQUNORCxRQUFRQyxLQUFLLENBQUMsOEJBQThCQTtRQUNoRDtJQUNKO0lBQ0EsTUFBTThGLHNCQUFzQjtRQUN4QixJQUFJek8sU0FBUztZQUNULDJCQUEyQjtZQUMzQixNQUFNc0gsT0FBTyxNQUFNL0MscUJBQXFCNEosT0FBTyxDQUFDaE87WUFDaEQsSUFBSW1ILE1BQU07Z0JBQ045QyxrQkFBa0I4QztZQUN0QjtRQUNKLE9BQU87WUFDSCxNQUFNaUgseUJBQXlCO2dCQUMzQnBHLFdBQVc7b0JBQ1BsRCxJQUFJLENBQUM5RTtnQkFDVDtZQUNKO1FBQ0o7SUFDSjtJQUNBLE1BQU11Tyx3QkFBd0IsQ0FBQy9KO1FBQzNCdEIsYUFBYWpILDRDQUFLQSxDQUFDdUksTUFBTXJCLE1BQU0sQ0FBQztJQUNwQztJQUNBLE1BQU1xTCx5QkFBeUIsQ0FBQ2hLO1FBQzVCbkIsY0FBY3BILDRDQUFLQSxDQUFDdUksTUFBTXJCLE1BQU0sQ0FBQztJQUNyQztJQUNBLE1BQU1zTCxlQUFlLENBQUNDO1lBRWRoUCxrREFBQUEsNENBTUFpUCw4Q0FBQUE7UUFQSixNQUFNQSxtQkFDRmpQLDBCQUFBQSxxQ0FBQUEsNkNBQUFBLGNBQWVrUCwyQkFBMkIsY0FBMUNsUCxrRUFBQUEsbURBQUFBLDJDQUE0Q21GLEtBQUssY0FBakRuRix1RUFBQUEsaURBQW1EK0csTUFBTSxDQUNyRCxDQUFDb0ksT0FDR0EsS0FBS0MsY0FBYyxLQUFLO1FBRXBDLElBQ0lILENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCdEgsTUFBTSxJQUFHLEtBQzNCc0gsRUFBQUEscUJBQUFBLGdCQUFnQixDQUFDLEVBQUUsY0FBbkJBLDBDQUFBQSwrQ0FBQUEsbUJBQXFCSSx5QkFBeUIsY0FBOUNKLG1FQUFBQSw2Q0FBZ0Q5SixLQUFLLENBQUM0QixNQUFNLENBQ3hELENBQUN1SSxRQUNHQSxNQUFNTixTQUFTLEtBQUtBLGFBQWFNLE1BQU14SCxNQUFNLEtBQUssT0FDeERILE1BQU0sSUFBRyxHQUNiO1lBQ0UsT0FBTztRQUNYO1FBQ0EsT0FBTztJQUNYO0lBQ0EsTUFBTTRILHVCQUF1QixDQUFDbEo7UUFDMUIsOENBQThDO1FBQzlDLElBQUksQ0FBQ0EsT0FBTztRQUVaLHVFQUF1RTtRQUN2RSxJQUFJQSxNQUFNQSxLQUFLLEVBQUU7WUFDYix5Q0FBeUM7WUFDekMxRixZQUFZO2dCQUNSLEdBQUdELFFBQVE7Z0JBQ1grRSxlQUFlLENBQUNZLE1BQU1BLEtBQUs7Z0JBQzNCUixLQUFLO2dCQUNMRSxNQUFNO1lBQ1Y7UUFDSixPQUFPLElBQ0hNLE1BQU1qQyxRQUFRLEtBQUtvTCxhQUNuQm5KLE1BQU1oQyxTQUFTLEtBQUttTCxXQUN0QjtZQUNFLGtDQUFrQztZQUNsQzdPLFlBQVk7Z0JBQ1IsR0FBR0QsUUFBUTtnQkFDWCtFLGVBQWU7Z0JBQ2ZJLEtBQUtRLE1BQU1qQyxRQUFRO2dCQUNuQjJCLE1BQU1NLE1BQU1oQyxTQUFTO1lBQ3pCO1FBQ0o7SUFDSjtJQUNBLE1BQU1vTCwyQkFBMkIsQ0FBQ3BKO1FBQzlCMUYsWUFBWTtZQUNSLEdBQUdELFFBQVE7WUFDWCtFLGVBQWU7WUFDZkksS0FBS1EsTUFBTWpDLFFBQVE7WUFDbkIyQixNQUFNTSxNQUFNaEMsU0FBUztRQUN6QjtJQUNKO0lBQ0EzSCxnREFBU0EsQ0FBQztRQUNOLElBQUkrQyxlQUFlO1lBQ2ZnQixnQkFBZ0JoQjtZQUNoQnNMLGdCQUFnQnRMLDBCQUFBQSxvQ0FBQUEsY0FBZTJGLEVBQUU7UUFDckM7UUFDQSxJQUFJNUUsY0FBYztZQUNkdUssZ0JBQWdCdksseUJBQUFBLG1DQUFBQSxhQUFjNEUsRUFBRTtRQUNwQztJQUNKLEdBQUc7UUFBQzNGO1FBQWVlO0tBQWE7SUFDaEM5RCxnREFBU0EsQ0FBQztRQUNOLElBQUksQ0FBQzRELGFBQWEsR0FBRztZQUNqQnNPO1FBQ0o7SUFDSixHQUFHO1FBQUN0TztLQUFXO0lBQ2Y1RCxnREFBU0EsQ0FBQztRQUNOLElBQUlELHFEQUFPQSxDQUFDaUUsV0FBVztZQUNuQkMsWUFBWTtnQkFDUixHQUFHRCxRQUFRO2dCQUNYNEMsVUFBVTlEO1lBQ2Q7UUFDSjtJQUNKLEdBQUc7UUFBQ2tCO0tBQVM7SUFDYixNQUFNZ1AsbUJBQW1CO1FBQ3JCLG1EQUFtRDtRQUNuRCxNQUFNaFAsV0FBVyxNQUFNNEQsa0JBQWtCZ0ssT0FBTyxDQUFDL087UUFDakRvQixZQUFZRDtRQUNaLHFDQUFxQztRQUNyQyxNQUFNaVAsUUFBUSxNQUFNckwsa0JBQWtCa0YsTUFBTTtRQUM1QzNILGlCQUFpQjhOO0lBQ3JCO0lBQ0FqVCxnREFBU0EsQ0FBQztRQUNOLElBQUl5RCxTQUFTO1lBQ1R1UDtRQUNKO0lBQ0osR0FBRztRQUFDdlA7S0FBUTtJQUVaLE1BQU0sQ0FBQ2tJLG1DQUFtQyxHQUFHdEssNERBQVdBLENBQ3BEUCw2RkFBc0NBLEVBQ3RDO1FBQ0krSixhQUFhLENBQUNDO2dCQUVTbkg7WUFEbkIsTUFBTW9ILE9BQU9ELFNBQVNhLGtDQUFrQztZQUN4RCxJQUFJWixLQUFLckMsRUFBRSxHQUFHLE1BQUsvRSx3QkFBQUEsbUNBQUFBLCtCQUFBQSxZQUFhdUgsZUFBZSxjQUE1QnZILG1EQUFBQSw2QkFBOEI4RSxLQUFLLEdBQUU7Z0JBQ3BEckUsZUFBZTtvQkFDWCxHQUFHVCxXQUFXO29CQUNkdUgsaUJBQWlCO3dCQUNiLEdBQUd2SCxZQUFZdUgsZUFBZTt3QkFDOUJ6QyxPQUFPOytCQUFJOUUsWUFBWXVILGVBQWUsQ0FBQ3pDLEtBQUs7NEJBQUVzQzt5QkFBSztvQkFDdkQ7Z0JBQ0o7WUFDSixPQUFPO2dCQUNIb0IsUUFBUUMsS0FBSyxDQUNULDRDQUNBdEI7WUFFUjtRQUNKO1FBQ0F1QixTQUFTLENBQUNEO1lBQ05ELFFBQVFDLEtBQUssQ0FBQyw0Q0FBNENBO1FBQzlEO0lBQ0o7SUFHSixNQUFNLENBQUM4RyxtQ0FBbUMsR0FBRzdSLDREQUFXQSxDQUNwRE4sNkZBQXNDQSxFQUN0QztRQUNJOEosYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVNvSSxrQ0FBa0M7WUFDeEQsSUFBSW5JLEtBQUtyQyxFQUFFLEdBQUcsR0FBRztvQkFNRS9FO2dCQUxmUyxlQUFlO29CQUNYLEdBQUdULFdBQVc7b0JBQ2R1SCxpQkFBaUI7d0JBQ2IsR0FBR3ZILFlBQVl1SCxlQUFlO3dCQUM5QnpDLE9BQU87K0JBQ0E5RSx3QkFBQUEsbUNBQUFBLCtCQUFBQSxZQUFhdUgsZUFBZSxjQUE1QnZILG1EQUFBQSw2QkFBOEI4RSxLQUFLLENBQUM0QixNQUFNLENBQ3pDLENBQUNjLGlCQUNHQSxlQUFlRywwQkFBMEIsS0FDekNQLEtBQUtPLDBCQUEwQjs0QkFFdkM7Z0NBQ0ksR0FBR1AsSUFBSTs0QkFDWDt5QkFDSDtvQkFDTDtnQkFDSjtZQUNKLE9BQU87Z0JBQ0hvQixRQUFRQyxLQUFLLENBQ1QsNENBQ0F0QjtZQUVSO1FBQ0o7UUFDQXVCLFNBQVMsQ0FBQ0Q7WUFDTkQsUUFBUUMsS0FBSyxDQUFDLDRDQUE0Q0E7UUFDOUQ7SUFDSjtJQUdKLE1BQU0rRyxnQkFBZ0I7UUFDbEIsTUFBTUMsYUFBYWxPLGNBQWNtRixNQUFNLENBQUMsQ0FBQ2dKO2dCQUNyQ3JQO21CQUFBQSxxQkFBQUEsZ0NBQUFBLDBCQUFBQSxTQUFVMEMsYUFBYSxjQUF2QjFDLDhDQUFBQSx3QkFBeUJzUCxRQUFRLENBQUNELEtBQUszSyxFQUFFOztRQUU3QyxPQUFPMEssV0FDRi9MLEdBQUcsQ0FBQyxDQUFDZ007WUFDRixPQUFPQSxLQUFLRSx3QkFBd0IsQ0FBQzlLLEtBQUssQ0FBQ3dDLE1BQU0sR0FBRyxJQUM5QztnQkFDSXZDLElBQUkySyxLQUFLM0ssRUFBRTtnQkFDWCtCLE9BQU80SSxLQUFLNUksS0FBSztnQkFDakIrSSxRQUFRSCxLQUFLRSx3QkFBd0IsQ0FBQzlLLEtBQUs7WUFDL0MsSUFDQTtRQUNWLEdBQ0M0QixNQUFNLENBQUMsQ0FBQ2dKLE9BQWNBLFFBQVE7SUFDdkM7SUFFQSxNQUFNSSx3QkFBd0IsQ0FBQ2IsT0FBWVMsTUFBV2pJO1lBV3BDekg7UUFWZCxJQUFJLENBQUNDLFlBQVk7WUFDYixNQUFNOFAsaUJBQWlCOU4scUJBQXFCeUUsTUFBTSxDQUM5QyxDQUFDYyxpQkFBd0JBLGVBQWVJLE9BQU8sS0FBS3FILE1BQU1sSyxFQUFFO1lBRWhFN0Msd0JBQXdCO21CQUNqQjZOO2dCQUNIO29CQUFFbkksU0FBU3FILE1BQU1sSyxFQUFFO29CQUFFMEMsUUFBUUE7Z0JBQU87YUFDdkM7WUFDRDtRQUNKO1FBQ0EsTUFBTTNDLFFBQVE5RSx3QkFBQUEsbUNBQUFBLCtCQUFBQSxZQUFhdUgsZUFBZSxjQUE1QnZILG1EQUFBQSw2QkFBOEI4RSxLQUFLO1FBQ2pELE1BQU1rTCxnQkFBZ0JsTCxrQkFBQUEsNEJBQUFBLE1BQU9nRCxJQUFJLENBQzdCLENBQUNOLGlCQUNHQSxlQUFlRywwQkFBMEIsS0FBS3NILE1BQU1sSyxFQUFFO1FBRTlELElBQUksQ0FBQ0QsU0FBUyxDQUFDa0wsZUFBZTtZQUMxQmhJLG1DQUFtQztnQkFDL0JDLFdBQVc7b0JBQ1BDLE9BQU87d0JBQ0hULFFBQVFBLFNBQVMsT0FBTzt3QkFDeEJDLG1CQUFtQnpIO3dCQUNuQjBILDRCQUE0QnNILE1BQU1sSyxFQUFFO29CQUN4QztnQkFDSjtZQUNKO1lBQ0E7UUFDSjtRQUVBLElBQUlELE1BQU13QyxNQUFNLEdBQUcsS0FBSzBJLGVBQWU7WUFDbkMsTUFBTUMsVUFBVUQsY0FBY2pMLEVBQUU7WUFDaEN3SyxtQ0FBbUM7Z0JBQy9CdEgsV0FBVztvQkFDUEMsT0FBTzt3QkFDSG5ELElBQUksQ0FBQ2tMO3dCQUNMeEksUUFBUUEsU0FBUyxPQUFPO3dCQUN4QkMsbUJBQW1Cekg7d0JBQ25CMEgsNEJBQTRCc0gsTUFBTWxLLEVBQUU7b0JBQ3hDO2dCQUNKO1lBQ0o7UUFDSixPQUFPO1lBQ0hpRCxtQ0FBbUM7Z0JBQy9CQyxXQUFXO29CQUNQQyxPQUFPO3dCQUNIVCxRQUFRQSxTQUFTLE9BQU87d0JBQ3hCQyxtQkFBbUJ6SDt3QkFDbkIwSCw0QkFBNEJzSCxNQUFNbEssRUFBRTtvQkFDeEM7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7SUFFQSxNQUFNbUwsaUJBQWlCLENBQUNqQjtZQVNBalAsb0NBQUFBO1FBUnBCLElBQUlpQyxxQkFBcUJxRixNQUFNLEdBQUcsR0FBRztZQUNqQyxNQUFNNkksY0FBY2xPLHFCQUFxQjZGLElBQUksQ0FDekMsQ0FBQ04saUJBQXdCQSxlQUFlSSxPQUFPLElBQUlxSCxNQUFNbEssRUFBRTtZQUUvRCxJQUFJb0wsYUFBYTtnQkFDYixPQUFPQSxZQUFZMUksTUFBTSxHQUFHLE9BQU87WUFDdkM7UUFDSjtRQUNBLE1BQU0wSSxjQUFjblEsd0JBQUFBLG1DQUFBQSwrQkFBQUEsWUFBYXVILGVBQWUsY0FBNUJ2SCxvREFBQUEscUNBQUFBLDZCQUE4QjhFLEtBQUssY0FBbkM5RSx5REFBQUEsbUNBQXFDOEgsSUFBSSxDQUN6RCxDQUFDTixpQkFDR0EsZUFBZUcsMEJBQTBCLElBQUlzSCxNQUFNbEssRUFBRTtRQUU3RCxPQUFPb0wsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhMUksTUFBTSxLQUFJO0lBQ2xDO0lBRUEsTUFBTTJJLG1CQUFtQixDQUFDbkI7WUFDRGpQLG9DQUFBQTtRQUFyQixNQUFNcVEsZUFBZXJRLHdCQUFBQSxtQ0FBQUEsK0JBQUFBLFlBQWF1SCxlQUFlLGNBQTVCdkgsb0RBQUFBLHFDQUFBQSw2QkFBOEI4RSxLQUFLLGNBQW5DOUUseURBQUFBLG1DQUFxQzhILElBQUksQ0FDMUQsQ0FBQ04saUJBQ0dBLGVBQWVHLDBCQUEwQixJQUFJc0gsTUFBTWxLLEVBQUU7UUFFN0QsSUFBSTVDLG1CQUFtQm1GLE1BQU0sR0FBRyxHQUFHO1lBQy9CLE1BQU0rSSxlQUFlbE8sbUJBQW1CMkYsSUFBSSxDQUN4QyxDQUFDTixpQkFBd0JBLGVBQWVJLE9BQU8sSUFBSXFILE1BQU1sSyxFQUFFO1lBRS9EekMsa0JBQWtCK04sQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjeEksT0FBTyxLQUFJO1FBQy9DLE9BQU87WUFDSHZGLGtCQUFrQitOLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY3hJLE9BQU8sS0FBSTtRQUMvQztRQUNBckYsZ0JBQWdCeU07UUFDaEJ2TSx1QkFBdUIyTjtRQUN2QnpOLG9CQUFvQjtJQUN4QjtJQUVBLE1BQU0wTixhQUFhLENBQUNyQjtZQVNLalAsb0NBQUFBO1FBUnJCLElBQUltQyxtQkFBbUJtRixNQUFNLEdBQUcsR0FBRztZQUMvQixNQUFNK0ksZUFBZWxPLG1CQUFtQjJGLElBQUksQ0FDeEMsQ0FBQ04saUJBQXdCQSxlQUFlSSxPQUFPLElBQUlxSCxNQUFNbEssRUFBRTtZQUUvRCxJQUFJc0wsY0FBYztnQkFDZCxPQUFPQSxhQUFheEksT0FBTztZQUMvQjtRQUNKO1FBQ0EsTUFBTXdJLGVBQWVyUSx3QkFBQUEsbUNBQUFBLCtCQUFBQSxZQUFhdUgsZUFBZSxjQUE1QnZILG9EQUFBQSxxQ0FBQUEsNkJBQThCOEUsS0FBSyxjQUFuQzlFLHlEQUFBQSxtQ0FBcUM4SCxJQUFJLENBQzFELENBQUNOLGlCQUNHQSxlQUFlRywwQkFBMEIsSUFBSXNILE1BQU1sSyxFQUFFO1FBRTdELE9BQU9zTCxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWN4SSxPQUFPLEtBQUlvSCxNQUFNcEgsT0FBTztJQUNqRDtJQUVBLE1BQU0wSSxvQkFBb0I7UUFDdEIsSUFBSSxDQUFDdFEsWUFBWTtZQUNiLE1BQU1vUSxlQUFlbE8sbUJBQW1CdUUsTUFBTSxDQUMxQyxDQUFDYyxpQkFDR0EsZUFBZUksT0FBTyxLQUFLckYsYUFBYXdDLEVBQUU7WUFFbEQzQyxzQkFBc0I7bUJBQ2ZpTztnQkFDSDtvQkFBRXpJLFNBQVNyRixhQUFhd0MsRUFBRTtvQkFBRThDLFNBQVN4RjtnQkFBZTthQUN2RDtZQUNETyxvQkFBb0I7WUFDcEI7UUFDSjtRQUNBLElBQUlILHFCQUFxQjtnQkFZUHpDO1lBWGR1UCxtQ0FBbUM7Z0JBQy9CdEgsV0FBVztvQkFDUEMsT0FBTzt3QkFDSG5ELElBQUl0QyxvQkFBb0JzQyxFQUFFO3dCQUMxQjJDLG1CQUFtQnpIO3dCQUNuQjBILDRCQUE0QnBGLGFBQWF3QyxFQUFFO3dCQUMzQzhDLFNBQVN4RjtvQkFDYjtnQkFDSjtZQUNKO1lBRUEsTUFBTXlDLFFBQVE5RSx3QkFBQUEsbUNBQUFBLCtCQUFBQSxZQUFhdUgsZUFBZSxjQUE1QnZILG1EQUFBQSw2QkFBOEI4RSxLQUFLO1lBQ2pELElBQUlBLE9BQU87Z0JBQ1ByRSxlQUFlO29CQUNYLEdBQUdULFdBQVc7b0JBQ2R1SCxpQkFBaUI7d0JBQ2IsR0FBR3ZILFlBQVl1SCxlQUFlO3dCQUM5QnpDLE9BQU87K0JBQ0FBLE1BQU00QixNQUFNLENBQ1gsQ0FBQ2MsaUJBQ0dBLGVBQWVHLDBCQUEwQixLQUN6Q3BGLGFBQWF3QyxFQUFFOzRCQUV2QjtnQ0FDSSxHQUFHdEMsbUJBQW1CO2dDQUN0Qm9GLFNBQVN4Rjs0QkFDYjt5QkFDSDtvQkFDTDtnQkFDSjtZQUNKO1FBQ0osT0FBTztZQUNIMkYsbUNBQW1DO2dCQUMvQkMsV0FBVztvQkFDUEMsT0FBTzt3QkFDSFIsbUJBQW1Cekg7d0JBQ25CMEgsNEJBQTRCcEYsYUFBYXdDLEVBQUU7d0JBQzNDOEMsU0FBU3hGO29CQUNiO2dCQUNKO1lBQ0o7UUFDSjtRQUNBTyxvQkFBb0I7SUFDeEI7SUFFQSxxQkFDSSw4REFBQzROO1FBQUlDLFdBQVU7O1lBQ1YsQ0FBQ3BRLFlBQVlKLGFBQWEsa0JBQ3ZCLDhEQUFDekQsOEVBQTJCQTs7OzswQ0FFNUIsOERBQUNnVTtnQkFBSUMsV0FBVTs7a0NBQ1gsOERBQUNoUyx3REFBS0E7d0JBQUNvSCxPQUFNO3dCQUFrQjZLLFVBQVU3UTs7MENBQ3JDLDhEQUFDdEQsaUdBQVlBO2dDQUNUbVUsVUFBVTdRO2dDQUNWQyxTQUFTQTtnQ0FDVGtHLEtBQUssRUFBRTNGLHFCQUFBQSwrQkFBQUEsU0FBVTJDLFNBQVM7Z0NBQzFCMk4sVUFBVWpFO2dDQUNWbkosaUJBQWlCQTs7Ozs7OzBDQUVyQiw4REFBQ3FOO2dDQUFNSCxXQUFVOzBDQUNaMVAsaUJBQWlCOEIsV0FBV0csU0FBUzs7Ozs7Ozs7Ozs7O2tDQUc5Qyw4REFBQ3ZFLHdEQUFLQTt3QkFBQ29ILE9BQU07d0JBQWU2SyxVQUFVN1E7a0NBQ2xDLDRFQUFDcEQsdUZBQXVCQTs0QkFDcEJxRCxTQUFTQTs0QkFDVGtHLEtBQUssRUFBRTNGLHFCQUFBQSwrQkFBQUEsU0FBVXVFLE9BQU87NEJBQ3hCK0wsVUFBVXhEOzRCQUNWNUosaUJBQWlCQTs7Ozs7Ozs7Ozs7a0NBSXpCLDhEQUFDOUUsd0RBQUtBO3dCQUFDb0gsT0FBTTt3QkFBaUI2SyxVQUFVN1E7a0NBQ3BDLDRFQUFDMlE7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDMVMsaUZBQStCQTtvQ0FDNUIrQixTQUFTQTtvQ0FDVGtHLEtBQUssRUFBRTNGLHFCQUFBQSwrQkFBQUEsU0FBVTBDLGFBQWE7b0NBQzlCNE4sVUFBVTFEO29DQUNWcE4sUUFBUUE7Ozs7OztnQ0FFWGdELFdBQVdFLGFBQWEsa0JBQ3JCLDhEQUFDNk47b0NBQU1ILFdBQVU7OENBQ1o1TixXQUFXRSxhQUFhOzs7Ozs7Z0NBSWhDMUMsWUFDR2tCLGNBQWNtRixNQUFNLENBQ2hCLENBQUNnSjt3Q0FDR3JQOzJDQUFBQSxDQUFBQSxxQkFBQUEsZ0NBQUFBLDBCQUFBQSxTQUFVMEMsYUFBYSxjQUF2QjFDLDhDQUFBQSx3QkFBeUJzUCxRQUFRLENBQzdCRCxLQUFLM0ssRUFBRSxNQUNOMkssS0FBS21CLFNBQVM7bUNBQ3pCdkosTUFBTSxHQUFHLG1CQUNQLDhEQUFDNUksMERBQU1BO29DQUNIb1MsU0FBUTtvQ0FDUkwsV0FBVTtvQ0FDVk0sU0FBUyxJQUNMclAscUJBQXFCOzhDQUN2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXRCLDhEQUFDakQsd0RBQUtBO3dCQUFDb0gsT0FBTTt3QkFBdUI2SyxVQUFVN1E7a0NBQ3pDd0IsY0FBZXJCLENBQUFBLGVBQWVDLGVBQWUsb0JBQzFDLDhEQUFDbkMsOERBQVFBOzRCQUNMa1QsU0FBUzNQLGNBQWMsRUFBRTs0QkFDekIyRSxPQUNJaEcsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhZ0wsb0JBQW9CLE1BQ2pDLFdBQ00zSix1QkFBQUEsaUNBQUFBLFdBQVlxRixNQUFNLENBQ2QsQ0FBQ0MsU0FDRyxDQUFDQSxPQUFPWCxLQUFLLEtBQ2IsRUFBQzNGLHFCQUFBQSwrQkFBQUEsU0FBVTRDLFFBQVEsRUFDMUIsQ0FBQyxFQUFFLEdBQ0pqRCxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFnTCxvQkFBb0IsTUFDL0IsWUFDQTtnQ0FDSW5GLE9BQU87Z0NBQ1BHLEtBQUssRUFBRWhHLHdCQUFBQSxrQ0FBQUEsWUFBYWdMLG9CQUFvQjs0QkFDNUMsSUFDQWhMLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYWdMLG9CQUFvQixNQUMvQixVQUNBO2dDQUNJbkYsT0FBTztnQ0FDUEcsS0FBSyxFQUFFaEcsd0JBQUFBLGtDQUFBQSxZQUFhZ0wsb0JBQW9COzRCQUM1QyxJQUNBaEwsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhZ0wsb0JBQW9CLE1BQzNCLGNBQ0poTCxDQUFBQSx3QkFBQUEsbUNBQUFBLGdDQUFBQSxZQUFhaVIsZ0JBQWdCLGNBQTdCalIsb0RBQUFBLDhCQUNNK0UsRUFBRSxJQUFHLElBQ1g7Z0NBQ0ljLEtBQUssRUFBRTdGLHdCQUFBQSxtQ0FBQUEsaUNBQUFBLFlBQ0RpUixnQkFBZ0IsY0FEZmpSLHFEQUFBQSwrQkFFRDhHLEtBQUs7Z0NBQ1hkLEtBQUssRUFBRWhHLHdCQUFBQSxtQ0FBQUEsaUNBQUFBLFlBQ0RpUixnQkFBZ0IsY0FEZmpSLHFEQUFBQSwrQkFDaUIrRSxFQUFFOzRCQUM5QixJQUNBMUQsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZeUcsSUFBSSxDQUNaLENBQUNuQixTQUNHLENBQUNBLE9BQU9YLEtBQUssS0FDYixDQUFDN0csY0FDSjs0QkFFckJ3UixVQUFVNUM7NEJBQ1ZtRCxhQUFZOzRCQUNaQyxZQUFZOzRCQUNaQyxpQkFBZ0I7Ozs7Ozs7Ozs7O2tDQUk1Qiw4REFBQ1I7d0JBQU1ILFdBQVU7a0NBQ1oxUCxpQkFBaUI4QixXQUFXSSxRQUFROzs7Ozs7b0JBR3hDeUwsYUFBYSwyQ0FDViw4REFBQ2pRLHdEQUFLQTt3QkFDRm9ILE9BQU07d0JBQ042SyxVQUFVN1E7a0NBQ1YsNEVBQUNyQix3REFBS0E7NEJBQ0Z1RyxJQUFHOzRCQUNIc00sTUFBSzs0QkFDTDNCLE1BQUs7NEJBQ0w0QixZQUFZLEVBQUVqUixxQkFBQUEsK0JBQUFBLFNBQVU2RSxTQUFTOzRCQUNqQ3VMLFdBQVk7NEJBQ1pTLGFBQVk7NEJBQ1pQLFVBQVV4VSxzREFBUUEsQ0FBQyxTQUFVb1YsQ0FBQztnQ0FDMUJqUixZQUFZO29DQUNSLEdBQUdELFFBQVE7b0NBQ1g2RSxXQUFXcU0sRUFBRUMsTUFBTSxDQUFDeEwsS0FBSztnQ0FDN0I7NEJBQ0osR0FBRzs7Ozs7Ozs7Ozs7b0JBSWQwSSxhQUFhLDZCQUNUQSxhQUFhLDRDQUNWLDhEQUFDalEsd0RBQUtBO3dCQUNGb0gsT0FBTTt3QkFDTjRLLFdBQVU7d0JBQ1ZDLFVBQVU3UTs7Ozs7O29CQUlyQjZPLGFBQWEsMkNBQ1YsOERBQUNqUSx3REFBS0E7d0JBQUNvSCxPQUFNO3dCQUF5QjZLLFVBQVU3UTtrQ0FDNUMsNEVBQUM3Qix5REFBU0E7NEJBQ055VCxNQUFNdk87NEJBQ053TyxrQkFBa0JsRDs0QkFDbEJtRCxRQUFPOzRCQUNQaEQsV0FBVTs7Ozs7Ozs7Ozs7b0JBS3JCRCxhQUFhLDRDQUNWLDhEQUFDalEsd0RBQUtBO3dCQUFDb0gsT0FBTTt3QkFBdUI2SyxVQUFVN1E7a0NBQzFDLDRFQUFDN0IseURBQVNBOzRCQUNOeVQsTUFBTXBPOzRCQUNOcU8sa0JBQWtCakQ7NEJBQ2xCa0QsUUFBTzs0QkFDUGhELFdBQVU7Ozs7Ozs7Ozs7O29CQUtwQixFQUFDeE8sZ0JBQWdCRSxRQUFPLG1CQUN0Qiw4REFBQ3BDLGdEQUFNQTt3QkFDSDhHLElBQUc7d0JBQ0htTSxhQUFZO3dCQUNaM0ssb0JBQW9CQTt3QkFDcEJoRyxTQUFTQTt3QkFDVG1RLFVBQVU3UTs7Ozs7O29CQVlqQm9CLG1CQUFtQnFHLE1BQU0sR0FBRyxtQkFDekI7OzBDQUNJLDhEQUFDdEksK0NBQUVBOzBDQUFDOzs7Ozs7MENBQ0osOERBQUN3UjtnQ0FBSUMsV0FBVTswQ0FDVnhQLG1CQUFtQnlDLEdBQUcsQ0FDbkIsQ0FBQzBDLFFBQWFxSDt3Q0FXRXRNLHdCQUtBQTt5REFmWiw4REFBQ3pFLGlFQUFZQTt3Q0FFVG9LLE9BQU9WLE9BQU9QLEtBQUs7d0NBQ25CTyxRQUFRQSxPQUFPUCxLQUFLO3dDQUNwQjJILFVBQVVwSCxPQUFPSixLQUFLO3dDQUN0QnVILG9CQUNJQTt3Q0FFSjdCLFdBQVc7NENBQ1BwRixhQUFhLEdBQ1RuRix5QkFBQUEsaUJBQWlCMkcsSUFBSSxDQUNqQixDQUFDOEosTUFDR0EsSUFBSXpMLFFBQVEsS0FDWkMsT0FBT0osS0FBSyxlQUhwQjdFLDZDQUFBQSx1QkFJR2tGLGFBQWE7NENBQ3BCdEIsRUFBRSxHQUFFNUQsMEJBQUFBLGlCQUFpQjJHLElBQUksQ0FDckIsQ0FBQzhKLE1BQ0dBLElBQUl6TCxRQUFRLEtBQ1pDLE9BQU9KLEtBQUssZUFIaEI3RSw4Q0FBQUEsd0JBSUR3RCxFQUFFO3dDQUNUO3dDQUNBOUUsUUFBUUE7dUNBcEJINE47Ozs7Ozs7Ozs7Ozs7a0NBMkI3Qiw4REFBQytDO3dCQUFJQyxXQUFVOzswQ0FDWCw4REFBQy9SLDBEQUFNQTtnQ0FDSG9TLFNBQVE7Z0NBQ1JlLFVBQVU1Viw0RkFBU0E7Z0NBQ25COFUsU0FBU3pSOzBDQUFZOzs7Ozs7MENBR3pCLDhEQUFDWiwwREFBTUE7Z0NBQ0hvUyxTQUFRO2dDQUNSZSxVQUFVN1YsNEZBQUtBO2dDQUNmK1UsU0FBU2xSLFNBQVMsS0FBTyxJQUFJb0w7Z0NBQzdCeUYsVUFDSXpKLHdDQUNBMkI7MENBRUgzSSxlQUFlLElBQUksU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU03Qyw4REFBQ3RCLHdEQUFLQTtnQkFBQ21ULE1BQU1yUTtnQkFBbUJzUSxjQUFjclE7MEJBQzFDLDRFQUFDOUMsK0RBQVlBO29CQUFDb1QsTUFBSztvQkFBUXZCLFdBQVU7O3NDQUNqQyw4REFBQzVSLDhEQUFXQTtzQ0FDUiw0RUFBQ0MsNkRBQVVBOzBDQUFDOzs7Ozs7Ozs7OztzQ0FFaEIsOERBQUNDLDREQUFTQTtzQ0FDTHNCLFlBQ0drQixjQUNLbUYsTUFBTSxDQUNILENBQUNnSjtvQ0FDR3JQO3VDQUFBQSxDQUFBQSxxQkFBQUEsZ0NBQUFBLDBCQUFBQSxTQUFVMEMsYUFBYSxjQUF2QjFDLDhDQUFBQSx3QkFBeUJzUCxRQUFRLENBQzdCRCxLQUFLM0ssRUFBRSxNQUNOMkssS0FBS21CLFNBQVM7K0JBRTFCbk4sR0FBRyxDQUFDLENBQUNnTSxxQkFDRiw4REFBQ2M7b0NBRUdDLFdBQVU7O3NEQUNWLDhEQUFDd0I7NENBQUd4QixXQUFVO3NEQUNUZixLQUFLNUksS0FBSzs7Ozs7O3NEQUVmLDhEQUFDMEo7NENBRUcwQix5QkFBeUI7Z0RBQ3JCQyxRQUFRekMsS0FBS21CLFNBQVM7NENBQzFCOzJDQUhLbkIsS0FBSzNLLEVBQUU7Ozs7OzttQ0FOWDJLLEtBQUszSyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFpQnBEO0dBaC9DTTlGOztRQXVDYTVCLDJEQUFhQTtRQTJKeEJLLHdEQUFXQTtRQXFEWEEsd0RBQVdBO1FBa0JYQyx5REFBWUE7UUFrQ1pELHdEQUFXQTtRQVNYQSx3REFBV0E7UUEwRldBLHdEQUFXQTtRQVdYQSx3REFBV0E7UUE4UU1DLHlEQUFZQTtRQU9uREQsd0RBQVdBO1FBc0JYQSx3REFBV0E7UUEyS1FDLHlEQUFZQTtRQVlBQSx5REFBWUE7UUF3SEZELHdEQUFXQTtRQTBCWEEsd0RBQVdBOzs7S0E3Z0N0RHVCO0FBay9DTiwrREFBZUEsaUJBQWlCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvbG9nYm9vay9mb3Jtcy9jcmV3LXRyYWluaW5nLWV2ZW50LnRzeD85M2FmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5pbXBvcnQgeyBDaGVjaywgQ2FsZW5kYXIsIEFycm93TGVmdCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IGRheWpzIGZyb20gJ2RheWpzJ1xyXG5pbXBvcnQgeyBkZWJvdW5jZSwgaXNFbXB0eSB9IGZyb20gJ2xvZGFzaCdcclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgQ3Jld0Ryb3Bkb3duIGZyb20gJy4uLy4uLy4uLy4uL2NvbXBvbmVudHMvZmlsdGVyL2NvbXBvbmVudHMvY3Jldy1kcm9wZG93bi9jcmV3LWRyb3Bkb3duJ1xyXG5pbXBvcnQgeyBUcmFpbmluZ1Nlc3Npb25Gb3JtU2tlbGV0b24gfSBmcm9tICcuLi8uLi8uLi8uLi9jb21wb25lbnRzL3NrZWxldG9ucydcclxuaW1wb3J0IENyZXdNdWx0aVNlbGVjdERyb3Bkb3duIGZyb20gJy4uLy4uL2NyZXcvbXVsdGlzZWxlY3QtZHJvcGRvd24vbXVsdGlzZWxlY3QtZHJvcGRvd24nXHJcbmltcG9ydCBTaWduYXR1cmVDYW52YXMgZnJvbSAncmVhY3Qtc2lnbmF0dXJlLWNhbnZhcydcclxuaW1wb3J0IHsgZ2V0U2lnbmF0dXJlVXJsIH0gZnJvbSAnQC9hcHAvbGliL2FjdGlvbnMnXHJcbmltcG9ydCBTaWduYXR1cmVQYWQgZnJvbSAnQC9jb21wb25lbnRzL3NpZ25hdHVyZS1wYWQnXHJcbmltcG9ydCB7XHJcbiAgICBDUkVBVEVfVFJBSU5JTkdfU0VTU0lPTixcclxuICAgIFVQREFURV9UUkFJTklOR19TRVNTSU9OLFxyXG4gICAgQ1JFQVRFX01FTUJFUl9UUkFJTklOR19TSUdOQVRVUkUsXHJcbiAgICBVUERBVEVfTUVNQkVSX1RSQUlOSU5HX1NJR05BVFVSRSxcclxuICAgIENSRUFURV9UUkFJTklOR19TRVNTSU9OX0RVRSxcclxuICAgIFVQREFURV9UUkFJTklOR19TRVNTSU9OX0RVRSxcclxuICAgIENyZWF0ZVRyaXBFdmVudCxcclxuICAgIFVwZGF0ZVRyaXBFdmVudCxcclxuICAgIENSRUFURV9DVVNUT01JU0VEX0NPTVBPTkVOVF9GSUVMRF9EQVRBLFxyXG4gICAgVVBEQVRFX0NVU1RPTUlTRURfQ09NUE9ORU5UX0ZJRUxEX0RBVEEsXHJcbn0gZnJvbSAnQC9hcHAvbGliL2dyYXBoUUwvbXV0YXRpb24nXHJcbmltcG9ydCB7IHVzZU1lZGlhUXVlcnkgfSBmcm9tICdAcmVhY3R1c2VzL2NvcmUnXHJcbmltcG9ydCB7XHJcbiAgICBHRVRfTUVNQkVSX1RSQUlOSU5HX1NJR05BVFVSRVMsXHJcbiAgICBHZXRUcmlwRXZlbnQsXHJcbiAgICBSRUFEX09ORV9UUkFJTklOR19TRVNTSU9OX0RVRSxcclxuICAgIFRSQUlOSU5HX1NFU1NJT05fQllfSUQsXHJcbn0gZnJvbSAnQC9hcHAvbGliL2dyYXBoUUwvcXVlcnknXHJcbmltcG9ydCB7IHVzZU11dGF0aW9uLCB1c2VMYXp5UXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCdcclxuaW1wb3J0IHsgZ2V0VHJhaW5pbmdUeXBlQnlJRCwgZ2V0VHJhaW5pbmdUeXBlcyB9IGZyb20gJ0AvYXBwL2xpYi9hY3Rpb25zJ1xyXG5pbXBvcnQgeyBDb21ib2JveCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jb21ib0JveCdcclxuXHJcbmltcG9ydCBUcmFpbmluZ1R5cGVNdWx0aVNlbGVjdERyb3Bkb3duIGZyb20gJy4uLy4uL2NyZXctdHJhaW5pbmcvdHlwZS1tdWx0aXNlbGVjdC1kcm9wZG93bidcclxuaW1wb3J0IExvY2F0aW9uRmllbGQgZnJvbSAnLi4vY29tcG9uZW50cy9sb2NhdGlvbidcclxuaW1wb3J0IFRpbWVGaWVsZCBmcm9tICcuLi9jb21wb25lbnRzL3RpbWUnXHJcbmltcG9ydCBFZGl0b3IgZnJvbSAnLi4vLi4vZWRpdG9yJ1xyXG5pbXBvcnQgVHJhaW5pbmdUeXBlTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvdHJhaW5pbmdUeXBlJ1xyXG5pbXBvcnQgTWVtYmVyVHJhaW5pbmdfU2lnbmF0dXJlTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvbWVtYmVyVHJhaW5pbmdfU2lnbmF0dXJlJ1xyXG5pbXBvcnQgVHJpcEV2ZW50TW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvdHJpcEV2ZW50J1xyXG5pbXBvcnQgVHJhaW5pbmdTZXNzaW9uRHVlTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvdHJhaW5pbmdTZXNzaW9uRHVlJ1xyXG5pbXBvcnQgVHJhaW5pbmdTZXNzaW9uTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvdHJhaW5pbmdTZXNzaW9uJ1xyXG5pbXBvcnQgeyBnZW5lcmF0ZVVuaXF1ZUlkIH0gZnJvbSAnQC9hcHAvb2ZmbGluZS9oZWxwZXJzL2Z1bmN0aW9ucydcclxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXHJcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJ1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xyXG5pbXBvcnQge1xyXG4gICAgU2hlZXQsXHJcbiAgICBTaGVldENvbnRlbnQsXHJcbiAgICBTaGVldEhlYWRlcixcclxuICAgIFNoZWV0VGl0bGUsXHJcbiAgICBTaGVldEJvZHksXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NoZWV0J1xyXG5pbXBvcnQgeyBINCB9IGZyb20gJ0AvY29tcG9uZW50cy91aSdcclxuXHJcbmNvbnN0IENyZXdUcmFpbmluZ0V2ZW50ID0gKHtcclxuICAgIHRyYWluaW5nVHlwZUlkID0gMCxcclxuICAgIHZlc3NlbElkID0gMCxcclxuICAgIHNlbGVjdGVkRXZlbnQgPSBmYWxzZSxcclxuICAgIGN1cnJlbnRUcmlwID0gZmFsc2UsXHJcbiAgICBjbG9zZU1vZGFsLFxyXG4gICAgdXBkYXRlVHJpcFJlcG9ydCxcclxuICAgIHRyaXBSZXBvcnQsXHJcbiAgICBjcmV3TWVtYmVycyxcclxuICAgIG1hc3RlcklELFxyXG4gICAgbG9nQm9va0NvbmZpZyxcclxuICAgIHZlc3NlbHMsXHJcbiAgICBsb2NrZWQsXHJcbiAgICBvZmZsaW5lID0gZmFsc2UsXHJcbiAgICBsb2dCb29rU3RhcnREYXRlLFxyXG59OiB7XHJcbiAgICB0cmFpbmluZ1R5cGVJZDogbnVtYmVyXHJcbiAgICB2ZXNzZWxJZDogbnVtYmVyXHJcbiAgICBzZWxlY3RlZEV2ZW50OiBhbnlcclxuICAgIGN1cnJlbnRUcmlwOiBhbnlcclxuICAgIGNsb3NlTW9kYWw6IGFueVxyXG4gICAgdXBkYXRlVHJpcFJlcG9ydDogYW55XHJcbiAgICB0cmlwUmVwb3J0OiBhbnlcclxuICAgIGNyZXdNZW1iZXJzOiBhbnlcclxuICAgIG1hc3RlcklEOiBhbnlcclxuICAgIGxvZ0Jvb2tDb25maWc6IGFueVxyXG4gICAgdmVzc2VsczogYW55XHJcbiAgICBsb2NrZWQ6IGFueVxyXG4gICAgb2ZmbGluZT86IGJvb2xlYW5cclxuICAgIGxvZ0Jvb2tTdGFydERhdGU6IGFueVxyXG59KSA9PiB7XHJcbiAgICBjb25zdCBbdHJhaW5pbmdJRCwgc2V0VHJhaW5pbmdJRF0gPSB1c2VTdGF0ZTxhbnk+KDApXHJcbiAgICBjb25zdCBbY3VycmVudEV2ZW50LCBzZXRDdXJyZW50RXZlbnRdID0gdXNlU3RhdGU8YW55PihzZWxlY3RlZEV2ZW50KVxyXG4gICAgY29uc3QgW3RyYWluaW5nLCBzZXRUcmFpbmluZ10gPSB1c2VTdGF0ZTxhbnk+KHt9KVxyXG4gICAgY29uc3QgW2NvbnRlbnQsIHNldENvbnRlbnRdID0gdXNlU3RhdGU8YW55PignJylcclxuICAgIGNvbnN0IFtyYXdUcmFpbmluZywgc2V0UmF3VHJhaW5pbmddID0gdXNlU3RhdGU8YW55PigpXHJcbiAgICBjb25zdCBbdHJhaW5pbmdEYXRlLCBzZXRUcmFpbmluZ0RhdGVdID0gdXNlU3RhdGUoXHJcbiAgICAgICAgbmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoKSxcclxuICAgIClcclxuICAgIGNvbnN0IGlzV2lkZSA9IHVzZU1lZGlhUXVlcnkoJyhtaW4td2lkdGg6IDY0MHB4KScpXHJcbiAgICBjb25zdCBbaGFzRm9ybUVycm9ycywgc2V0SGFzRm9ybUVycm9yc10gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IFtzZWxlY3RlZE1lbWJlckxpc3QsIHNldFNlbGVjdGVkTWVtYmVyTGlzdF0gPSB1c2VTdGF0ZShbXSBhcyBhbnlbXSlcclxuICAgIGNvbnN0IFtzaWduYXR1cmVNZW1iZXJzLCBzZXRTaWduYXR1cmVNZW1iZXJzXSA9IHVzZVN0YXRlKFtdIGFzIGFueVtdKVxyXG4gICAgY29uc3QgW3Zlc3NlbExpc3QsIHNldFZlc3NlbExpc3RdID0gdXNlU3RhdGU8YW55PigpXHJcbiAgICBjb25zdCBbdHJhaW5pbmdUeXBlcywgc2V0VHJhaW5pbmdUeXBlc10gPSB1c2VTdGF0ZTxhbnk+KFtdKVxyXG4gICAgY29uc3QgW29wZW5WaWV3UHJvY2VkdXJlLCBzZXRPcGVuVmlld1Byb2NlZHVyZV0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IFtvcGVuRGVzY3JpcHRpb25QYW5lbCwgc2V0T3BlbkRlc2NyaXB0aW9uUGFuZWxdID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgICBjb25zdCBbZGVzY3JpcHRpb25QYW5lbENvbnRlbnQsIHNldERlc2NyaXB0aW9uUGFuZWxDb250ZW50XSA9IHVzZVN0YXRlKCcnKVxyXG4gICAgY29uc3QgW2Rlc2NyaXB0aW9uUGFuZWxIZWFkaW5nLCBzZXREZXNjcmlwdGlvblBhbmVsSGVhZGluZ10gPSB1c2VTdGF0ZSgnJylcclxuICAgIGNvbnN0IFtidWZmZXJQcm9jZWR1cmVDaGVjaywgc2V0QnVmZmVyUHJvY2VkdXJlQ2hlY2tdID0gdXNlU3RhdGU8YW55PihbXSlcclxuICAgIGNvbnN0IFtidWZmZXJGaWVsZENvbW1lbnQsIHNldEJ1ZmZlckZpZWxkQ29tbWVudF0gPSB1c2VTdGF0ZTxhbnk+KFtdKVxyXG4gICAgY29uc3QgW2N1cnJlbnRDb21tZW50LCBzZXRDdXJyZW50Q29tbWVudF0gPSB1c2VTdGF0ZTxhbnk+KCcnKVxyXG4gICAgY29uc3QgW2N1cnJlbnRGaWVsZCwgc2V0Q3VycmVudEZpZWxkXSA9IHVzZVN0YXRlPGFueT4oJycpXHJcbiAgICBjb25zdCBbY3VycmVudEZpZWxkQ29tbWVudCwgc2V0Q3VycmVudEZpZWxkQ29tbWVudF0gPSB1c2VTdGF0ZTxhbnk+KCcnKVxyXG4gICAgY29uc3QgW29wZW5Db21tZW50QWxlcnQsIHNldE9wZW5Db21tZW50QWxlcnRdID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgICBjb25zdCBbZm9ybUVycm9ycywgc2V0Rm9ybUVycm9yc10gPSB1c2VTdGF0ZSh7XHJcbiAgICAgICAgVHJhaW5pbmdUeXBlczogJycsXHJcbiAgICAgICAgVHJhaW5lcklEOiAnJyxcclxuICAgICAgICBWZXNzZWxJRDogJycsXHJcbiAgICAgICAgRGF0ZTogJycsXHJcbiAgICB9KVxyXG4gICAgY29uc3QgW3N0YXJ0VGltZSwgc2V0U3RhcnRUaW1lXSA9IHVzZVN0YXRlPGFueT4oZGF5anMoKS5mb3JtYXQoJ0hIOm1tJykpXHJcbiAgICBjb25zdCBbZmluaXNoVGltZSwgc2V0RmluaXNoVGltZV0gPSB1c2VTdGF0ZTxhbnk+KGRheWpzKCkuZm9ybWF0KCdISDptbScpKVxyXG4gICAgY29uc3QgbWVtYmVySWRPcHRpb25zID0gW1xyXG4gICAgICAgIG1hc3RlcklELFxyXG4gICAgICAgIC4uLihBcnJheS5pc0FycmF5KGNyZXdNZW1iZXJzKVxyXG4gICAgICAgICAgICA/IGNyZXdNZW1iZXJzLm1hcCgobTogYW55KSA9PiBtLmNyZXdNZW1iZXJJRClcclxuICAgICAgICAgICAgOiBbXSksXHJcbiAgICBdXHJcbiAgICBjb25zdCBbY3VycmVudExvY2F0aW9uLCBzZXRDdXJyZW50TG9jYXRpb25dID0gdXNlU3RhdGU8YW55Pih7XHJcbiAgICAgICAgbGF0aXR1ZGU6ICcnLFxyXG4gICAgICAgIGxvbmdpdHVkZTogJycsXHJcbiAgICB9KVxyXG5cclxuICAgIGNvbnN0IHRyYWluaW5nVHlwZU1vZGVsID0gbmV3IFRyYWluaW5nVHlwZU1vZGVsKClcclxuICAgIGNvbnN0IG1lbWJlclRyYWluaW5nX1NpZ25hdHVyZU1vZGVsID0gbmV3IE1lbWJlclRyYWluaW5nX1NpZ25hdHVyZU1vZGVsKClcclxuICAgIGNvbnN0IHRyaXBFdmVudE1vZGVsID0gbmV3IFRyaXBFdmVudE1vZGVsKClcclxuICAgIGNvbnN0IHRyYWluaW5nU2Vzc2lvbkR1ZU1vZGVsID0gbmV3IFRyYWluaW5nU2Vzc2lvbkR1ZU1vZGVsKClcclxuICAgIGNvbnN0IHRyYWluaW5nU2Vzc2lvbk1vZGVsID0gbmV3IFRyYWluaW5nU2Vzc2lvbk1vZGVsKClcclxuXHJcbiAgICBpZiAoIW9mZmxpbmUpIHtcclxuICAgICAgICBnZXRUcmFpbmluZ1R5cGVzKHNldFRyYWluaW5nVHlwZXMpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlU2V0VHJhaW5pbmcgPSAodDogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc3QgdERhdGUgPSBuZXcgRGF0ZSh0LmRhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpXHJcbiAgICAgICAgc2V0VHJhaW5pbmdEYXRlKHREYXRlKVxyXG4gICAgICAgIGNvbnN0IHRyYWluaW5nRGF0YSA9IHtcclxuICAgICAgICAgICAgSUQ6IHRyYWluaW5nSUQsXHJcbiAgICAgICAgICAgIERhdGU6IGRheWpzKHQuZGF0ZSkuZm9ybWF0KCdZWVlZLU1NLUREJyksXHJcbiAgICAgICAgICAgIE1lbWJlcnM6IHQubWVtYmVycy5ub2Rlcy5tYXAoKG06IGFueSkgPT4gbS5pZCksXHJcbiAgICAgICAgICAgIFRyYWluZXJJRDogdC50cmFpbmVyLmlkLFxyXG4gICAgICAgICAgICB0cmFpbmluZ1N1bW1hcnk6IHQudHJhaW5pbmdTdW1tYXJ5LFxyXG4gICAgICAgICAgICBUcmFpbmluZ1R5cGVzOiB0LnRyYWluaW5nVHlwZXMubm9kZXMubWFwKCh0OiBhbnkpID0+IHQuaWQpLFxyXG4gICAgICAgICAgICBWZXNzZWxJRDogdmVzc2VsSWQsXHJcbiAgICAgICAgICAgIEZ1ZWxMZXZlbDogdC5mdWVsTGV2ZWwgfHwgMCxcclxuICAgICAgICAgICAgR2VvTG9jYXRpb25JRDogdC5nZW9Mb2NhdGlvbklELFxyXG4gICAgICAgICAgICBTdGFydFRpbWU6IHQuc3RhcnRUaW1lLFxyXG4gICAgICAgICAgICBGaW5pc2hUaW1lOiB0LmZpbmlzaFRpbWUsXHJcbiAgICAgICAgICAgIExhdDogdC5sYXQsXHJcbiAgICAgICAgICAgIExvbmc6IHQubG9uZyxcclxuICAgICAgICB9XHJcbiAgICAgICAgc2V0Q29udGVudCh0LnRyYWluaW5nU3VtbWFyeSlcclxuICAgICAgICBzZXRTdGFydFRpbWUodC5zdGFydFRpbWUpXHJcbiAgICAgICAgc2V0RmluaXNoVGltZSh0LmZpbmlzaFRpbWUpXHJcbiAgICAgICAgc2V0UmF3VHJhaW5pbmcodClcclxuICAgICAgICBzZXRUcmFpbmluZyh0cmFpbmluZ0RhdGEpXHJcbiAgICAgICAgaWYgKCt0Lmdlb0xvY2F0aW9uSUQgPiAwKSB7XHJcbiAgICAgICAgICAgIHNldEN1cnJlbnRMb2NhdGlvbih7XHJcbiAgICAgICAgICAgICAgICBsYXRpdHVkZTogdC5nZW9Mb2NhdGlvbi5sYXQsXHJcbiAgICAgICAgICAgICAgICBsb25naXR1ZGU6IHQuZ2VvTG9jYXRpb24ubG9uZyxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBzZXRDdXJyZW50TG9jYXRpb24oe1xyXG4gICAgICAgICAgICAgICAgbGF0aXR1ZGU6IHQubGF0LFxyXG4gICAgICAgICAgICAgICAgbG9uZ2l0dWRlOiB0LmxvbmcsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBtZW1iZXJzID1cclxuICAgICAgICAgICAgdC5tZW1iZXJzLm5vZGVzLm1hcCgobTogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgbGFiZWw6IGAke20uZmlyc3ROYW1lID8/ICcnfSAke20uc3VybmFtZSA/PyAnJ31gLFxyXG4gICAgICAgICAgICAgICAgdmFsdWU6IG0uaWQsXHJcbiAgICAgICAgICAgIH0pKSB8fCBbXVxyXG4gICAgICAgIHNldFNlbGVjdGVkTWVtYmVyTGlzdChtZW1iZXJzKVxyXG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZXMgPSB0LnNpZ25hdHVyZXMubm9kZXMubWFwKChzOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgIE1lbWJlcklEOiBzLm1lbWJlci5pZCxcclxuICAgICAgICAgICAgU2lnbmF0dXJlRGF0YTogcy5zaWduYXR1cmVEYXRhLFxyXG4gICAgICAgICAgICBJRDogcy5pZCxcclxuICAgICAgICB9KSlcclxuICAgICAgICBzZXRTaWduYXR1cmVNZW1iZXJzKHNpZ25hdHVyZXMpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlRWRpdG9yQ2hhbmdlID0gKG5ld0NvbnRlbnQ6IGFueSkgPT4ge1xyXG4gICAgICAgIHNldENvbnRlbnQobmV3Q29udGVudClcclxuICAgIH1cclxuXHJcbiAgICAvLyBjb25zdCBoYW5kbGVTZXRWZXNzZWxzID0gKGRhdGE6IGFueSkgPT4ge1xyXG4gICAgLy8gICAgIGNvbnN0IGFjdGl2ZVZlc3NlbHMgPSBkYXRhPy5maWx0ZXIoKHZlc3NlbDogYW55KSA9PiAhdmVzc2VsLmFyY2hpdmVkKVxyXG4gICAgLy8gICAgIGNvbnN0IGZvcm1hdHRlZERhdGEgPSBbXHJcbiAgICAvLyAgICAgICAgIHtcclxuICAgIC8vICAgICAgICAgICAgIGxhYmVsOiAnT3RoZXInLFxyXG4gICAgLy8gICAgICAgICAgICAgdmFsdWU6ICdPdGhlcicsXHJcbiAgICAvLyAgICAgICAgIH0sXHJcbiAgICAvLyAgICAgICAgIHtcclxuICAgIC8vICAgICAgICAgICAgIGxhYmVsOiAnRGVza3RvcC9zaG9yZScsXHJcbiAgICAvLyAgICAgICAgICAgICB2YWx1ZTogJ09uc2hvcmUnLFxyXG4gICAgLy8gICAgICAgICB9LFxyXG4gICAgLy8gICAgICAgICAuLi5hY3RpdmVWZXNzZWxzLm1hcCgodmVzc2VsOiBhbnkpID0+ICh7XHJcbiAgICAvLyAgICAgICAgICAgICB2YWx1ZTogdmVzc2VsLmlkLFxyXG4gICAgLy8gICAgICAgICAgICAgbGFiZWw6IHZlc3NlbC50aXRsZSxcclxuICAgIC8vICAgICAgICAgfSkpLFxyXG4gICAgLy8gICAgIF1cclxuICAgIC8vICAgICBzZXRWZXNzZWxzKGZvcm1hdHRlZERhdGEpXHJcbiAgICAvLyB9XHJcblxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAodmVzc2Vscykge1xyXG4gICAgICAgICAgICBjb25zdCBhY3RpdmVWZXNzZWxzID0gdmVzc2Vscz8uZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgKHZlc3NlbDogYW55KSA9PiAhdmVzc2VsLmFyY2hpdmVkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZERhdGEgPSBbXHJcbiAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICdPdGhlcicsXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICdPdGhlcicsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsOiAnRGVza3RvcC9zaG9yZScsXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICdPbnNob3JlJyxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAuLi5hY3RpdmVWZXNzZWxzLm1hcCgodmVzc2VsOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHZlc3NlbC5pZCxcclxuICAgICAgICAgICAgICAgICAgICBsYWJlbDogdmVzc2VsLnRpdGxlLFxyXG4gICAgICAgICAgICAgICAgfSkpLFxyXG4gICAgICAgICAgICBdXHJcbiAgICAgICAgICAgIHNldFZlc3NlbExpc3QoZm9ybWF0dGVkRGF0YSlcclxuICAgICAgICB9XHJcbiAgICB9LCBbdmVzc2Vsc10pXHJcblxyXG4gICAgLy8gY29uc3QgW3F1ZXJ5VmVzc2Vsc10gPSB1c2VMYXp5UXVlcnkoVkVTU0VMX0xJU1QsIHtcclxuICAgIC8vICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgIC8vICAgICBvbkNvbXBsZXRlZDogKHF1ZXJ5VmVzc2VsUmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgLy8gICAgICAgICBpZiAocXVlcnlWZXNzZWxSZXNwb25zZS5yZWFkVmVzc2Vscy5ub2Rlcykge1xyXG4gICAgLy8gICAgICAgICAgICAgaGFuZGxlU2V0VmVzc2VscyhxdWVyeVZlc3NlbFJlc3BvbnNlLnJlYWRWZXNzZWxzLm5vZGVzKVxyXG4gICAgLy8gICAgICAgICB9XHJcbiAgICAvLyAgICAgfSxcclxuICAgIC8vICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgLy8gICAgICAgICBjb25zb2xlLmVycm9yKCdxdWVyeVZlc3NlbHMgZXJyb3InLCBlcnJvcilcclxuICAgIC8vICAgICB9LFxyXG4gICAgLy8gfSlcclxuXHJcbiAgICBjb25zdCBbXHJcbiAgICAgICAgbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb24sXHJcbiAgICAgICAgeyBsb2FkaW5nOiBtdXRhdGlvbkNyZWF0ZVRyYWluaW5nU2Vzc2lvbkxvYWRpbmcgfSxcclxuICAgIF0gPSB1c2VNdXRhdGlvbihDUkVBVEVfVFJBSU5JTkdfU0VTU0lPTiwge1xyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuY3JlYXRlVHJhaW5pbmdTZXNzaW9uXHJcbiAgICAgICAgICAgIGlmIChkYXRhLmlkID4gMCkge1xyXG4gICAgICAgICAgICAgICAgaWYgKGJ1ZmZlclByb2NlZHVyZUNoZWNrLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcm9jZWR1cmVGaWVsZHMgPSBidWZmZXJQcm9jZWR1cmVDaGVjay5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogcHJvY2VkdXJlRmllbGQuc3RhdHVzID8gJ09rJyA6ICdOb3RfT2snLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbklEOiBkYXRhLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1c3RvbWlzZWRDb21wb25lbnRGaWVsZElEOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9jZWR1cmVGaWVsZC5maWVsZElkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbW1lbnQ6IGJ1ZmZlckZpZWxkQ29tbWVudC5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoY29tbWVudDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29tbWVudC5maWVsZElkID09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9jZWR1cmVGaWVsZC5maWVsZElkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk/LmNvbW1lbnQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIHByb2NlZHVyZUZpZWxkcy5mb3JFYWNoKChwcm9jZWR1cmVGaWVsZDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNyZWF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGEoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHByb2NlZHVyZUZpZWxkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdJRChkYXRhLmlkKVxyXG4gICAgICAgICAgICAgICAgdXBkYXRlVHJhaW5pbmdTZXNzaW9uRHVlcygpXHJcbiAgICAgICAgICAgICAgICB1cGRhdGVTaWduYXR1cmVzKGRhdGEuaWQpXHJcbiAgICAgICAgICAgICAgICBoYW5kbGVFZGl0b3JDaGFuZ2UoZGF0YS50cmFpbmluZ1N1bW1hcnkpXHJcbiAgICAgICAgICAgICAgICB1cGRhdGVUcmlwRXZlbnQoe1xyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6ICtjdXJyZW50RXZlbnQ/LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnRDYXRlZ29yeTogJ0NyZXdUcmFpbmluZycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3VHJhaW5pbmdJRDogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIGNsb3NlTW9kYWwoKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb24gZXJyb3InLCByZXNwb25zZSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb24gZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IFtcclxuICAgICAgICBtdXRhdGlvblVwZGF0ZVRyYWluaW5nU2Vzc2lvbixcclxuICAgICAgICB7IGxvYWRpbmc6IG11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uTG9hZGluZyB9LFxyXG4gICAgXSA9IHVzZU11dGF0aW9uKFVQREFURV9UUkFJTklOR19TRVNTSU9OLCB7XHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS51cGRhdGVUcmFpbmluZ1Nlc3Npb25cclxuICAgICAgICAgICAgaWYgKGRhdGEuaWQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICB1cGRhdGVUcmFpbmluZ1Nlc3Npb25EdWVzKClcclxuICAgICAgICAgICAgICAgIHVwZGF0ZVNpZ25hdHVyZXModHJhaW5pbmdJRClcclxuICAgICAgICAgICAgICAgIGhhbmRsZUVkaXRvckNoYW5nZShkYXRhLnRyYWluaW5nU3VtbWFyeSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ211dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uIGVycm9yJywgcmVzcG9uc2UpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ211dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uIGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcbiAgICBjb25zdCBbXHJcbiAgICAgICAgcmVhZE9uZVRyYWluaW5nU2Vzc2lvbkR1ZSxcclxuICAgICAgICB7IGxvYWRpbmc6IHJlYWRPbmVUcmFpbmluZ1Nlc3Npb25EdWVMb2FkaW5nIH0sXHJcbiAgICBdID0gdXNlTGF6eVF1ZXJ5KFJFQURfT05FX1RSQUlOSU5HX1NFU1NJT05fRFVFLCB7XHJcbiAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIHJldHVybiByZXNwb25zZS5yZWFkT25lVHJhaW5pbmdTZXNzaW9uRHVlLmRhdGFcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3JlYWRPbmVUcmFpbmluZ1Nlc3Npb25EdWVMb2FkaW5nIGVycm9yOicsIGVycm9yKVxyXG4gICAgICAgICAgICByZXR1cm4gbnVsbFxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG4gICAgY29uc3QgZ2V0VHJhaW5pbmdTZXNzaW9uRHVlV2l0aFZhcmlhYmxlcyA9IGFzeW5jIChcclxuICAgICAgICB2YXJpYWJsZXM6IGFueSA9IHt9LFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiBhbnksXHJcbiAgICApID0+IHtcclxuICAgICAgICBpZiAob2ZmbGluZSkge1xyXG4gICAgICAgICAgICBjb25zdCBhbGxEdWVzID0gYXdhaXQgdHJhaW5pbmdTZXNzaW9uRHVlTW9kZWwuZ2V0QWxsKClcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGFsbER1ZXMuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgKGl0ZW06IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBpdGVtLm1lbWJlcklEID09PSB2YXJpYWJsZXMuZmlsdGVyLm1lbWJlcklELmVxICYmXHJcbiAgICAgICAgICAgICAgICAgICAgaXRlbS52ZXNzZWxJRCA9PT0gdmFyaWFibGVzLmZpbHRlci52ZXNzZWxJRC5lcSAmJlxyXG4gICAgICAgICAgICAgICAgICAgIGl0ZW0udHJhaW5pbmdUeXBlSUQgPT09IHZhcmlhYmxlcy5maWx0ZXIudHJhaW5pbmdUeXBlSUQuZXEsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQoZGF0YSlcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjb25zdCB7IGRhdGEgfTogYW55ID0gYXdhaXQgcmVhZE9uZVRyYWluaW5nU2Vzc2lvbkR1ZSh7XHJcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHZhcmlhYmxlcyxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQoZGF0YS5yZWFkT25lVHJhaW5pbmdTZXNzaW9uRHVlKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbXHJcbiAgICAgICAgbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb25EdWUsXHJcbiAgICAgICAgeyBsb2FkaW5nOiBjcmVhdGVUcmFpbmluZ1Nlc3Npb25EdWVMb2FkaW5nIH0sXHJcbiAgICBdID0gdXNlTXV0YXRpb24oQ1JFQVRFX1RSQUlOSU5HX1NFU1NJT05fRFVFLCB7XHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7fSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdjcmVhdGVUcmFpbmluZ1Nlc3Npb25EdWUgZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IFtcclxuICAgICAgICBtdXRhdGlvblVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZSxcclxuICAgICAgICB7IGxvYWRpbmc6IHVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZUxvYWRpbmcgfSxcclxuICAgIF0gPSB1c2VNdXRhdGlvbihVUERBVEVfVFJBSU5JTkdfU0VTU0lPTl9EVUUsIHtcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHt9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3VwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG4gICAgY29uc3QgdXBkYXRlVHJhaW5pbmdTZXNzaW9uRHVlcyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICBjb25zdCB0cmFpbmluZ1Nlc3Npb25EdWVzOiBhbnkgPSBbXVxyXG4gICAgICAgIGNvbnN0IHZlc3NlbElEID0gdHJhaW5pbmcuVmVzc2VsSURcclxuICAgICAgICB0cmFpbmluZy5UcmFpbmluZ1R5cGVzLmZvckVhY2goKHQ6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCB0cmFpbmluZ0luZm8gPSB0cmFpbmluZ1R5cGVzLmZpbmQoKHR0OiBhbnkpID0+IHR0LmlkID09PSB0KVxyXG5cclxuICAgICAgICAgICAgaWYgKCFpc0VtcHR5KHRyYWluaW5nSW5mbykgJiYgdHJhaW5pbmdJbmZvLm9jY3Vyc0V2ZXJ5ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdHJhaW5pbmdUeXBlSUQgPSB0XHJcbiAgICAgICAgICAgICAgICBjb25zdCBuZXdEdWVEYXRlID0gZGF5anModHJhaW5pbmcuRGF0ZSkuYWRkKFxyXG4gICAgICAgICAgICAgICAgICAgIHRyYWluaW5nSW5mby5vY2N1cnNFdmVyeSxcclxuICAgICAgICAgICAgICAgICAgICAnZGF5JyxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIHRyYWluaW5nLk1lbWJlcnMuZm9yRWFjaCgobTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVtYmVySUQgPSBtXHJcbiAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlcy5wdXNoKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZHVlRGF0ZTogbmV3RHVlRGF0ZS5mb3JtYXQoJ1lZWVktTU0tREQnKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySUQ6IG1lbWJlcklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxJRDogdmVzc2VsSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZUlEOiB0cmFpbmluZ1R5cGVJRCxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgbGV0IHRyYWluaW5nU2Vzc2lvbkR1ZVdpdGhJRHM6IGFueSA9IFtdXHJcbiAgICAgICAgaWYgKCFpc0VtcHR5KHRyYWluaW5nU2Vzc2lvbkR1ZXMpKSB7XHJcbiAgICAgICAgICAgIGF3YWl0IFByb21pc2UuYWxsKFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlcy5tYXAoYXN5bmMgKGl0ZW06IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhcmlhYmxlcyA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJJRDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVxOiBpdGVtLm1lbWJlcklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElEOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXE6IGl0ZW0udmVzc2VsSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcTogaXRlbS50cmFpbmluZ1R5cGVJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG9uQ29tcGxldGVkID0gKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlV2l0aElEcy5wdXNoKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLml0ZW0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogcmVzcG9uc2U/LmlkID8/IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBhd2FpdCBnZXRUcmFpbmluZ1Nlc3Npb25EdWVXaXRoVmFyaWFibGVzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ29tcGxldGVkLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAoIWlzRW1wdHkodHJhaW5pbmdTZXNzaW9uRHVlV2l0aElEcykpIHtcclxuICAgICAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoXHJcbiAgICAgICAgICAgICAgICBBcnJheS5mcm9tKHRyYWluaW5nU2Vzc2lvbkR1ZVdpdGhJRHMpLm1hcChhc3luYyAoaXRlbTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFyaWFibGVzID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHsgaW5wdXQ6IGl0ZW0gfSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW0uaWQgPT09IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIG11dGF0aW9uQ3JlYXRlVHJhaW5pbmdTZXNzaW9uRHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCB0cmFpbmluZ1Nlc3Npb25EdWVNb2RlbC5zYXZlKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5pdGVtLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBnZW5lcmF0ZVVuaXF1ZUlkKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb25EdWUodmFyaWFibGVzKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIG11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uRHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCB0cmFpbmluZ1Nlc3Npb25EdWVNb2RlbC5zYXZlKGl0ZW0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBtdXRhdGlvblVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZSh2YXJpYWJsZXMpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9KSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbdXBkYXRlVHJpcEV2ZW50XSA9IHVzZU11dGF0aW9uKFVwZGF0ZVRyaXBFdmVudCwge1xyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2UpID0+IHtcclxuICAgICAgICAgICAgZ2V0Q3VycmVudEV2ZW50KGN1cnJlbnRFdmVudD8uaWQpXHJcbiAgICAgICAgICAgIHVwZGF0ZVRyaXBSZXBvcnQoe1xyXG4gICAgICAgICAgICAgICAgaWQ6IFsuLi50cmlwUmVwb3J0Lm1hcCgodHJpcDogYW55KSA9PiB0cmlwLmlkKSwgY3VycmVudFRyaXAuaWRdLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHRyaXAgZXZlbnQnLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IFtjcmVhdGVUcmlwRXZlbnRdID0gdXNlTXV0YXRpb24oQ3JlYXRlVHJpcEV2ZW50LCB7XHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuY3JlYXRlVHJpcEV2ZW50XHJcbiAgICAgICAgICAgIHNldEN1cnJlbnRFdmVudChkYXRhKVxyXG4gICAgICAgICAgICBzYXZlVHJhaW5pbmcoKVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHRyaXAgZXZlbnQnLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuXHJcbiAgICBjb25zdCBzYXZlVHJhaW5pbmcgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgaW5wdXQgPSB7XHJcbiAgICAgICAgICAgIGlkOiB0cmFpbmluZ0lELFxyXG4gICAgICAgICAgICBkYXRlOiB0cmFpbmluZy5EYXRlXHJcbiAgICAgICAgICAgICAgICA/IGRheWpzKGxvZ0Jvb2tTdGFydERhdGUpLmZvcm1hdCgnWVlZWS1NTS1ERCcpXHJcbiAgICAgICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICAgICBtZW1iZXJzOiB0cmFpbmluZy5NZW1iZXJzPy5qb2luKCcsJyksXHJcbiAgICAgICAgICAgIHRyYWluZXJJRDogdHJhaW5pbmcuVHJhaW5lcklELFxyXG4gICAgICAgICAgICB0cmFpbmluZ1N1bW1hcnk6IGNvbnRlbnQsXHJcbiAgICAgICAgICAgIHRyYWluaW5nVHlwZXM6IHRyYWluaW5nLlRyYWluaW5nVHlwZXM/LmpvaW4oJywnKSxcclxuICAgICAgICAgICAgdmVzc2VsSUQ6IHRyYWluaW5nPy5WZXNzZWxJRCxcclxuICAgICAgICAgICAgdHJhaW5pbmdMb2NhdGlvblR5cGU6IHRyYWluaW5nPy5WZXNzZWxJRFxyXG4gICAgICAgICAgICAgICAgPyB0cmFpbmluZy5WZXNzZWxJRCA9PT0gJ090aGVyJyB8fFxyXG4gICAgICAgICAgICAgICAgICB0cmFpbmluZy5WZXNzZWxJRCA9PT0gJ09uc2hvcmUnXHJcbiAgICAgICAgICAgICAgICAgICAgPyB0cmFpbmluZy5WZXNzZWxJRFxyXG4gICAgICAgICAgICAgICAgICAgIDogJ1Zlc3NlbCdcclxuICAgICAgICAgICAgICAgIDogJ0xvY2F0aW9uJyxcclxuICAgICAgICAgICAgZnVlbExldmVsOiBgJHt0cmFpbmluZy5GdWVsTGV2ZWx9YCxcclxuICAgICAgICAgICAgZ2VvTG9jYXRpb25JRDogdHJhaW5pbmcuR2VvTG9jYXRpb25JRCxcclxuICAgICAgICAgICAgc3RhcnRUaW1lOiBzdGFydFRpbWUsXHJcbiAgICAgICAgICAgIGZpbmlzaFRpbWU6IGZpbmlzaFRpbWUsXHJcbiAgICAgICAgICAgIGxhdDogYCR7dHJhaW5pbmcuTGF0fWAsXHJcbiAgICAgICAgICAgIGxvbmc6IGAke3RyYWluaW5nLkxvbmd9YCxcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHRyYWluaW5nSUQgPT09IDApIHtcclxuICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgIC8vIG11dGF0aW9uQ3JlYXRlVHJhaW5pbmdTZXNzaW9uXHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdHJhaW5pbmdTZXNzaW9uTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4uaW5wdXQsXHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IGdlbmVyYXRlVW5pcXVlSWQoKSxcclxuICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdJRChkYXRhLmlkKVxyXG4gICAgICAgICAgICAgICAgdXBkYXRlVHJhaW5pbmdTZXNzaW9uRHVlcygpXHJcbiAgICAgICAgICAgICAgICB1cGRhdGVTaWduYXR1cmVzKGRhdGEuaWQpXHJcbiAgICAgICAgICAgICAgICBoYW5kbGVFZGl0b3JDaGFuZ2UoZGF0YS50cmFpbmluZ1N1bW1hcnkpXHJcbiAgICAgICAgICAgICAgICAvLyB1cGRhdGVUcmlwRXZlbnRcclxuICAgICAgICAgICAgICAgIGF3YWl0IHRyaXBFdmVudE1vZGVsLnNhdmUoe1xyXG4gICAgICAgICAgICAgICAgICAgIGlkOiArY3VycmVudEV2ZW50Py5pZCxcclxuICAgICAgICAgICAgICAgICAgICBldmVudENhdGVnb3J5OiAnQ3Jld1RyYWluaW5nJyxcclxuICAgICAgICAgICAgICAgICAgICBjcmV3VHJhaW5pbmdJRDogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgYXdhaXQgZ2V0Q3VycmVudEV2ZW50KGN1cnJlbnRFdmVudD8uaWQpXHJcbiAgICAgICAgICAgICAgICB1cGRhdGVUcmlwUmVwb3J0KHtcclxuICAgICAgICAgICAgICAgICAgICBpZDogW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi50cmlwUmVwb3J0Lm1hcCgodHJpcDogYW55KSA9PiB0cmlwLmlkKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFRyaXAuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgXSxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICBjbG9zZU1vZGFsKClcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGF3YWl0IG11dGF0aW9uQ3JlYXRlVHJhaW5pbmdTZXNzaW9uKHtcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IGlucHV0LFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgIC8vIG11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uXHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdHJhaW5pbmdTZXNzaW9uTW9kZWwuc2F2ZShpbnB1dClcclxuICAgICAgICAgICAgICAgIHVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZXMoKVxyXG4gICAgICAgICAgICAgICAgdXBkYXRlU2lnbmF0dXJlcyh0cmFpbmluZ0lEKVxyXG4gICAgICAgICAgICAgICAgaGFuZGxlRWRpdG9yQ2hhbmdlKGRhdGEudHJhaW5pbmdTdW1tYXJ5KVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgYXdhaXQgbXV0YXRpb25VcGRhdGVUcmFpbmluZ1Nlc3Npb24oe1xyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpbnB1dDogaW5wdXQsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBjb25zdCBoYW5kbGVTYXZlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGxldCBoYXNFcnJvcnMgPSBmYWxzZVxyXG4gICAgICAgIGxldCBlcnJvcnMgPSB7XHJcbiAgICAgICAgICAgIFRyYWluaW5nVHlwZXM6ICcnLFxyXG4gICAgICAgICAgICBUcmFpbmVySUQ6ICcnLFxyXG4gICAgICAgICAgICBWZXNzZWxJRDogJycsXHJcbiAgICAgICAgICAgIERhdGU6ICcnLFxyXG4gICAgICAgIH1cclxuICAgICAgICBzZXRGb3JtRXJyb3JzKGVycm9ycylcclxuICAgICAgICAvLyBWYWxpZGF0ZSBUcmFpbmluZyBUeXBlcyAtIGNoZWNrIGlmIGVtcHR5IG9yIHVuZGVmaW5lZFxyXG4gICAgICAgIGlmICghdHJhaW5pbmcuVHJhaW5pbmdUeXBlcyB8fCBpc0VtcHR5KHRyYWluaW5nLlRyYWluaW5nVHlwZXMpKSB7XHJcbiAgICAgICAgICAgIGhhc0Vycm9ycyA9IHRydWVcclxuICAgICAgICAgICAgZXJyb3JzLlRyYWluaW5nVHlwZXMgPSAnTmF0dXJlIG9mIHRyYWluaW5nIGlzIHJlcXVpcmVkJ1xyXG5cclxuICAgICAgICAgICAgLy8gQ2xlYXIgYW55IHByZXZpb3VzIGVycm9yIHN0YXRlIGZvciB0aGlzIGZpZWxkXHJcbiAgICAgICAgICAgIHNldEZvcm1FcnJvcnMoKHByZXZFcnJvcnMpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAuLi5wcmV2RXJyb3JzLFxyXG4gICAgICAgICAgICAgICAgVHJhaW5pbmdUeXBlczogJ05hdHVyZSBvZiB0cmFpbmluZyBpcyByZXF1aXJlZCcsXHJcbiAgICAgICAgICAgIH0pKVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIC8vIENsZWFyIGFueSBwcmV2aW91cyBlcnJvciBmb3IgdGhpcyBmaWVsZCB3aGVuIHZhbGlkXHJcbiAgICAgICAgICAgIHNldEZvcm1FcnJvcnMoKHByZXZFcnJvcnMpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAuLi5wcmV2RXJyb3JzLFxyXG4gICAgICAgICAgICAgICAgVHJhaW5pbmdUeXBlczogJycsXHJcbiAgICAgICAgICAgIH0pKVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoISh0cmFpbmluZy5UcmFpbmVySUQgJiYgdHJhaW5pbmcuVHJhaW5lcklEID4gMCkpIHtcclxuICAgICAgICAgICAgaGFzRXJyb3JzID0gdHJ1ZVxyXG4gICAgICAgICAgICBlcnJvcnMuVHJhaW5lcklEID0gJ1RyYWluZXIgaXMgcmVxdWlyZWQnXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChcclxuICAgICAgICAgICAgIXRyYWluaW5nLlZlc3NlbElEICYmXHJcbiAgICAgICAgICAgICEodHJhaW5pbmcuVHJhaW5pbmdMb2NhdGlvbklEICYmIHRyYWluaW5nLlRyYWluaW5nTG9jYXRpb25JRCA+PSAwKVxyXG4gICAgICAgICkge1xyXG4gICAgICAgICAgICBoYXNFcnJvcnMgPSB0cnVlXHJcbiAgICAgICAgICAgIGVycm9ycy5WZXNzZWxJRCA9ICdMb2NhdGlvbiBpcyByZXF1aXJlZCdcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmICh0eXBlb2YgdHJhaW5pbmcuRGF0ZSA9PT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAgICAgdHJhaW5pbmcuRGF0ZSA9IGRheWpzKGxvZ0Jvb2tTdGFydERhdGUpLmZvcm1hdCgnWVlZWS1NTS1ERCcpXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAodHJhaW5pbmcuRGF0ZSA9PT0gbnVsbCB8fCAhZGF5anModHJhaW5pbmcuRGF0ZSkuaXNWYWxpZCgpKSB7XHJcbiAgICAgICAgICAgIGhhc0Vycm9ycyA9IHRydWVcclxuICAgICAgICAgICAgZXJyb3JzLkRhdGUgPSAnVGhlIGRhdGUgaXMgaW52YWxpZCdcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGhhc0Vycm9ycykge1xyXG4gICAgICAgICAgICBzZXRIYXNGb3JtRXJyb3JzKHRydWUpXHJcbiAgICAgICAgICAgIHNldEZvcm1FcnJvcnMoZXJyb3JzKVxyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGN1cnJlbnRFdmVudCkge1xyXG4gICAgICAgICAgICBpZiAob2ZmbGluZSkge1xyXG4gICAgICAgICAgICAgICAgLy8gdXBkYXRlVHJpcEV2ZW50XHJcbiAgICAgICAgICAgICAgICBhd2FpdCB0cmlwRXZlbnRNb2RlbC5zYXZlKHtcclxuICAgICAgICAgICAgICAgICAgICBpZDogK2N1cnJlbnRFdmVudC5pZCxcclxuICAgICAgICAgICAgICAgICAgICBldmVudENhdGVnb3J5OiAnQ3Jld1RyYWluaW5nJyxcclxuICAgICAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlTZWN0aW9uSUQ6IGN1cnJlbnRUcmlwLmlkLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICBhd2FpdCBnZXRDdXJyZW50RXZlbnQoY3VycmVudEV2ZW50Py5pZClcclxuICAgICAgICAgICAgICAgIHVwZGF0ZVRyaXBSZXBvcnQoe1xyXG4gICAgICAgICAgICAgICAgICAgIGlkOiBbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnRyaXBSZXBvcnQubWFwKCh0cmlwOiBhbnkpID0+IHRyaXAuaWQpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50VHJpcC5pZCxcclxuICAgICAgICAgICAgICAgICAgICBdLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHVwZGF0ZVRyaXBFdmVudCh7XHJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogK2N1cnJlbnRFdmVudC5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50Q2F0ZWdvcnk6ICdDcmV3VHJhaW5pbmcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9nQm9va0VudHJ5U2VjdGlvbklEOiBjdXJyZW50VHJpcC5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBzYXZlVHJhaW5pbmcoKVxyXG4gICAgICAgICAgICBjbG9zZU1vZGFsKClcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBpZiAob2ZmbGluZSkge1xyXG4gICAgICAgICAgICAgICAgLy8gY3JlYXRlVHJpcEV2ZW50XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0cmlwRXZlbnREYXRhID0gYXdhaXQgdHJpcEV2ZW50TW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IGdlbmVyYXRlVW5pcXVlSWQoKSxcclxuICAgICAgICAgICAgICAgICAgICBldmVudENhdGVnb3J5OiAnQ3Jld1RyYWluaW5nJyxcclxuICAgICAgICAgICAgICAgICAgICBsb2dCb29rRW50cnlTZWN0aW9uSUQ6IGN1cnJlbnRUcmlwLmlkLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIHNldEN1cnJlbnRFdmVudCh0cmlwRXZlbnREYXRhKVxyXG4gICAgICAgICAgICAgICAgc2F2ZVRyYWluaW5nKClcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNyZWF0ZVRyaXBFdmVudCh7XHJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudENhdGVnb3J5OiAnQ3Jld1RyYWluaW5nJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvZ0Jvb2tFbnRyeVNlY3Rpb25JRDogY3VycmVudFRyaXAuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgdXBkYXRlU2lnbmF0dXJlcyA9IChUcmFpbmluZ0lEOiBudW1iZXIpID0+IHtcclxuICAgICAgICBzaWduYXR1cmVNZW1iZXJzLmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgc2lnbmF0dXJlTWVtYmVycz8uZm9yRWFjaCgoc2lnbmF0dXJlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNoZWNrQW5kU2F2ZVNpZ25hdHVyZShzaWduYXR1cmUsIFRyYWluaW5nSUQpXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY2hlY2tBbmRTYXZlU2lnbmF0dXJlID0gYXN5bmMgKFxyXG4gICAgICAgIHNpZ25hdHVyZTogYW55LFxyXG4gICAgICAgIFRyYWluaW5nSUQ6IG51bWJlcixcclxuICAgICkgPT4ge1xyXG4gICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgIC8vIHF1ZXJ5R2V0TWVtYmVyVHJhaW5pbmdTaWduYXR1cmVzXHJcbiAgICAgICAgICAgIGNvbnN0IGFsbFNpZ25hdHVyZXMgPSBhd2FpdCBtZW1iZXJUcmFpbmluZ19TaWduYXR1cmVNb2RlbC5nZXRBbGwoKVxyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gYWxsU2lnbmF0dXJlcy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAoaXRlbTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIGl0ZW0ubWVtYmVySUQgPT09IHNpZ25hdHVyZS5NZW1iZXJJRCAmJlxyXG4gICAgICAgICAgICAgICAgICAgIGl0ZW0udHJhaW5pbmdTZXNzaW9uSUQgPT09IFRyYWluaW5nSUQsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICAgaWYgKGRhdGEubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgICAgLy8gbXV0YXRpb25VcGRhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZVxyXG4gICAgICAgICAgICAgICAgYXdhaXQgbWVtYmVyVHJhaW5pbmdfU2lnbmF0dXJlTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IGRhdGFbMF0uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgbWVtYmVySUQ6IHNpZ25hdHVyZS5NZW1iZXJJRCxcclxuICAgICAgICAgICAgICAgICAgICBzaWduYXR1cmVEYXRhOiBzaWduYXR1cmUuU2lnbmF0dXJlRGF0YSxcclxuICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1Nlc3Npb25JRDogVHJhaW5pbmdJRCxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAvLyBtdXRhdGlvbkNyZWF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlXHJcbiAgICAgICAgICAgICAgICBhd2FpdCBtZW1iZXJUcmFpbmluZ19TaWduYXR1cmVNb2RlbC5zYXZlKHtcclxuICAgICAgICAgICAgICAgICAgICBpZDogZ2VuZXJhdGVVbmlxdWVJZCgpLFxyXG4gICAgICAgICAgICAgICAgICAgIG1lbWJlcklEOiBzaWduYXR1cmUuTWVtYmVySUQsXHJcbiAgICAgICAgICAgICAgICAgICAgc2lnbmF0dXJlRGF0YTogc2lnbmF0dXJlLlNpZ25hdHVyZURhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uSUQ6IFRyYWluaW5nSUQsXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgYXdhaXQgcXVlcnlHZXRNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZXMoe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklEOiB7IGVxOiBzaWduYXR1cmUuTWVtYmVySUQgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uSUQ6IHsgaW46IFRyYWluaW5nSUQgfSxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIC50aGVuKChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLmRhdGEucmVhZE1lbWJlclRyYWluaW5nX1NpZ25hdHVyZXMubm9kZXNcclxuICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG11dGF0aW9uVXBkYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmUoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGRhdGFbMF0uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklEOiBzaWduYXR1cmUuTWVtYmVySUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25hdHVyZURhdGE6IHNpZ25hdHVyZS5TaWduYXR1cmVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1Nlc3Npb25JRDogVHJhaW5pbmdJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2lnbmF0dXJlLlNpZ25hdHVyZURhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG11dGF0aW9uQ3JlYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmUoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySUQ6IHNpZ25hdHVyZS5NZW1iZXJJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25hdHVyZURhdGE6IHNpZ25hdHVyZS5TaWduYXR1cmVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uSUQ6IFRyYWluaW5nSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgLmNhdGNoKChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgJ211dGF0aW9uR2V0TWVtYmVyVHJhaW5pbmdTaWduYXR1cmVzIGVycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3IsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgW3F1ZXJ5R2V0TWVtYmVyVHJhaW5pbmdTaWduYXR1cmVzXSA9IHVzZUxhenlRdWVyeShcclxuICAgICAgICBHRVRfTUVNQkVSX1RSQUlOSU5HX1NJR05BVFVSRVMsXHJcbiAgICApXHJcblxyXG4gICAgY29uc3QgW1xyXG4gICAgICAgIG11dGF0aW9uVXBkYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmUsXHJcbiAgICAgICAgeyBsb2FkaW5nOiBtdXRhdGlvblVwZGF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlTG9hZGluZyB9LFxyXG4gICAgXSA9IHVzZU11dGF0aW9uKFVQREFURV9NRU1CRVJfVFJBSU5JTkdfU0lHTkFUVVJFLCB7XHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS51cGRhdGVNZW1iZXJUcmFpbmluZ19TaWduYXR1cmVcclxuICAgICAgICAgICAgaWYgKGRhdGEuaWQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBzaWduYXR1cmVDb3VudCsrXHJcbiAgICAgICAgICAgICAgICAvLyBpZiAoc2lnbmF0dXJlQ291bnQgPT09IHNpZ25hdHVyZU1lbWJlcnMubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgICAgICAvLyB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFxyXG4gICAgICAgICAgICAgICAgICAgICdtdXRhdGlvblVwZGF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlIGVycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICByZXNwb25zZSxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignbXV0YXRpb25VcGRhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG5cclxuICAgIGNvbnN0IFtcclxuICAgICAgICBtdXRhdGlvbkNyZWF0ZU1lbWJlclRyYWluaW5nU2lnbmF0dXJlLFxyXG4gICAgICAgIHsgbG9hZGluZzogbXV0YXRpb25DcmVhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZUxvYWRpbmcgfSxcclxuICAgIF0gPSB1c2VNdXRhdGlvbihDUkVBVEVfTUVNQkVSX1RSQUlOSU5HX1NJR05BVFVSRSwge1xyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuY3JlYXRlTWVtYmVyVHJhaW5pbmdfU2lnbmF0dXJlXHJcbiAgICAgICAgICAgIGlmIChkYXRhLmlkID4gMCkge1xyXG4gICAgICAgICAgICAgICAgLy8gc2lnbmF0dXJlQ291bnQrK1xyXG4gICAgICAgICAgICAgICAgLy8gaWYgKHNpZ25hdHVyZUNvdW50ID09PSBzaWduYXR1cmVNZW1iZXJzLmxlbmd0aCkge1xyXG4gICAgICAgICAgICAgICAgLy8gfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICAgICAgICAgICAgICAnbXV0YXRpb25DcmVhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZSBlcnJvcicsXHJcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UsXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ211dGF0aW9uQ3JlYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmUgZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuXHJcbiAgICBjb25zdCBoYW5kbGVUcmFpbmluZ0RhdGVDaGFuZ2UgPSAoZGF0ZTogYW55KSA9PiB7XHJcbiAgICAgICAgc2V0VHJhaW5pbmdEYXRlKG5ldyBEYXRlKGRhdGUudG9TdHJpbmcoKSkudG9Mb2NhbGVEYXRlU3RyaW5nKCkpXHJcbiAgICAgICAgc2V0VHJhaW5pbmcoe1xyXG4gICAgICAgICAgICAuLi50cmFpbmluZyxcclxuICAgICAgICAgICAgRGF0ZTogZGF5anMoZGF0ZSkuZm9ybWF0KCdZWVlZLU1NLUREJyksXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuICAgIGNvbnN0IGhhbmRsZVRyYWluZXJDaGFuZ2UgPSAodHJhaW5lcjogYW55KSA9PiB7XHJcbiAgICAgICAgaWYgKCF0cmFpbmVyKSByZXR1cm4gLy8gQWRkIGVhcmx5IHJldHVybiBpZiB0cmFpbmVyIGlzIG51bGxcclxuXHJcbiAgICAgICAgLy8gQ2hlY2sgaWYgdHJhaW5lciBpcyBhbiBhcnJheSAobXVsdGlwbGUgc2VsZWN0aW9uKSBvciBhIHNpbmdsZSBvYmplY3RcclxuICAgICAgICBjb25zdCB0cmFpbmVyVmFsdWUgPSBBcnJheS5pc0FycmF5KHRyYWluZXIpXHJcbiAgICAgICAgICAgID8gdHJhaW5lci5sZW5ndGggPiAwXHJcbiAgICAgICAgICAgICAgICA/IHRyYWluZXJbMF0udmFsdWVcclxuICAgICAgICAgICAgICAgIDogbnVsbFxyXG4gICAgICAgICAgICA6IHRyYWluZXIudmFsdWVcclxuXHJcbiAgICAgICAgaWYgKCF0cmFpbmVyVmFsdWUpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBVc2UgU2V0KCkgdG8gcHJldmVudCBkdXBsaWNhdGUgdmFsdWVzLCB0aGVuIEFycmF5LmZyb20oKSB0byBjb252ZXJ0IGl0IHRvIGFuIGFycmF5XHJcbiAgICAgICAgY29uc3QgbWVtYmVyc1NldCA9IG5ldyBTZXQodHJhaW5pbmc/Lk1lbWJlcnMgfHwgW10pXHJcbiAgICAgICAgbWVtYmVyc1NldC5hZGQodHJhaW5lclZhbHVlKVxyXG4gICAgICAgIGNvbnN0IG1lbWJlcnMgPSBBcnJheS5mcm9tKG1lbWJlcnNTZXQpXHJcblxyXG4gICAgICAgIHNldFRyYWluaW5nKHtcclxuICAgICAgICAgICAgLi4udHJhaW5pbmcsXHJcbiAgICAgICAgICAgIFRyYWluZXJJRDogdHJhaW5lclZhbHVlLFxyXG4gICAgICAgICAgICBNZW1iZXJzOiBtZW1iZXJzLFxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgICAgIC8vIENyZWF0ZSBhIHByb3BlciB0cmFpbmVyIG9iamVjdCBmb3IgdGhlIHNlbGVjdGVkTWVtYmVyTGlzdFxyXG4gICAgICAgIGNvbnN0IHRyYWluZXJPYmplY3QgPSBBcnJheS5pc0FycmF5KHRyYWluZXIpID8gdHJhaW5lclswXSA6IHRyYWluZXJcclxuXHJcbiAgICAgICAgLy8gQ2hlY2sgaWYgdHJhaW5lciBpcyBhbHJlYWR5IGluIHNlbGVjdGVkTWVtYmVyTGlzdCB0byBhdm9pZCBkdXBsaWNhdGVzXHJcbiAgICAgICAgY29uc3QgaXNUcmFpbmVyQWxyZWFkeVNlbGVjdGVkID0gc2VsZWN0ZWRNZW1iZXJMaXN0LnNvbWUoXHJcbiAgICAgICAgICAgIChtZW1iZXI6IGFueSkgPT4gK21lbWJlci52YWx1ZSA9PT0gK3RyYWluZXJWYWx1ZSxcclxuICAgICAgICApXHJcblxyXG4gICAgICAgIGlmICghaXNUcmFpbmVyQWxyZWFkeVNlbGVjdGVkKSB7XHJcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTWVtYmVyTGlzdChbLi4uc2VsZWN0ZWRNZW1iZXJMaXN0LCB0cmFpbmVyT2JqZWN0XSlcclxuICAgICAgICAgICAgc2V0U2lnbmF0dXJlTWVtYmVycyhbXHJcbiAgICAgICAgICAgICAgICAuLi5zaWduYXR1cmVNZW1iZXJzLFxyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgIE1lbWJlcklEOiArdHJhaW5lclZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgIFNpZ25hdHVyZURhdGE6IG51bGwsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBdKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGNvbnN0IGhhbmRsZVRyYWluaW5nVHlwZUNoYW5nZSA9ICh0cmFpbmluZ1R5cGVzOiBhbnkpID0+IHtcclxuICAgICAgICAvLyBVcGRhdGUgdHJhaW5pbmcgc3RhdGUgd2l0aCBzZWxlY3RlZCB0eXBlc1xyXG4gICAgICAgIGNvbnN0IHNlbGVjdGVkVHlwZXMgPSAhaXNFbXB0eSh0cmFpbmluZ1R5cGVzKVxyXG4gICAgICAgICAgICA/IHRyYWluaW5nVHlwZXMubWFwKChpdGVtOiBhbnkpID0+IGl0ZW0udmFsdWUpXHJcbiAgICAgICAgICAgIDogW11cclxuXHJcbiAgICAgICAgc2V0VHJhaW5pbmcoe1xyXG4gICAgICAgICAgICAuLi50cmFpbmluZyxcclxuICAgICAgICAgICAgVHJhaW5pbmdUeXBlczogc2VsZWN0ZWRUeXBlcyxcclxuICAgICAgICB9KVxyXG5cclxuICAgICAgICAvLyBDbGVhciBlcnJvciBtZXNzYWdlIGlmIHZhbGlkIHNlbGVjdGlvbiBpcyBtYWRlXHJcbiAgICAgICAgaWYgKCFpc0VtcHR5KHNlbGVjdGVkVHlwZXMpKSB7XHJcbiAgICAgICAgICAgIHNldEZvcm1FcnJvcnMoKHByZXZFcnJvcnMpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAuLi5wcmV2RXJyb3JzLFxyXG4gICAgICAgICAgICAgICAgVHJhaW5pbmdUeXBlczogJycsXHJcbiAgICAgICAgICAgIH0pKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVNZW1iZXJDaGFuZ2UgPSAobWVtYmVyczogYW55KSA9PiB7XHJcbiAgICAgICAgLy8gRW5zdXJlIG1lbWJlcnMgaXMgYW4gYXJyYXlcclxuICAgICAgICBjb25zdCBtZW1iZXJzQXJyYXkgPSBBcnJheS5pc0FycmF5KG1lbWJlcnMpXHJcbiAgICAgICAgICAgID8gbWVtYmVyc1xyXG4gICAgICAgICAgICA6IFttZW1iZXJzXS5maWx0ZXIoQm9vbGVhbilcclxuXHJcbiAgICAgICAgLy8gTWFrZSBzdXJlIHdlJ3JlIGZpbHRlcmluZyB3aXRoIHZhbGlkIG1lbWJlciB2YWx1ZXNcclxuICAgICAgICBjb25zdCBzaWduYXR1cmVzID0gc2lnbmF0dXJlTWVtYmVycy5maWx0ZXIoKGl0ZW06IGFueSkgPT5cclxuICAgICAgICAgICAgbWVtYmVyc0FycmF5LnNvbWUoXHJcbiAgICAgICAgICAgICAgICAobTogYW55KSA9PiBtICYmIG0udmFsdWUgJiYgK20udmFsdWUgPT09IGl0ZW0uTWVtYmVySUQsXHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgKVxyXG5cclxuICAgICAgICAvLyBFeHRyYWN0IG1lbWJlciB2YWx1ZXMgc2FmZWx5XHJcbiAgICAgICAgY29uc3QgbWVtYmVyVmFsdWVzID0gbWVtYmVyc0FycmF5XHJcbiAgICAgICAgICAgIC5maWx0ZXIoKGl0ZW06IGFueSkgPT4gaXRlbSAmJiBpdGVtLnZhbHVlKVxyXG4gICAgICAgICAgICAubWFwKChpdGVtOiBhbnkpID0+IGl0ZW0udmFsdWUpXHJcblxyXG4gICAgICAgIHNldFRyYWluaW5nKHtcclxuICAgICAgICAgICAgLi4udHJhaW5pbmcsXHJcbiAgICAgICAgICAgIE1lbWJlcnM6IG1lbWJlclZhbHVlcyxcclxuICAgICAgICAgICAgLy8gU2lnbmF0dXJlczogc2lnbmF0dXJlcyxcclxuICAgICAgICB9KVxyXG4gICAgICAgIHNldFNlbGVjdGVkTWVtYmVyTGlzdChtZW1iZXJzQXJyYXkpXHJcbiAgICAgICAgc2V0U2lnbmF0dXJlTWVtYmVycyhzaWduYXR1cmVzKVxyXG4gICAgfVxyXG4gICAgY29uc3Qgb25TaWduYXR1cmVDaGFuZ2VkID0gKFxyXG4gICAgICAgIHNpZ25hdHVyZTogc3RyaW5nLFxyXG4gICAgICAgIG1lbWJlcj86IHN0cmluZyB8IHVuZGVmaW5lZCxcclxuICAgICAgICBtZW1iZXJJZD86IG51bWJlciB8IHVuZGVmaW5lZCxcclxuICAgICkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGluZGV4ID0gc2lnbmF0dXJlTWVtYmVycy5maW5kSW5kZXgoXHJcbiAgICAgICAgICAgIChvYmplY3QpID0+IG9iamVjdC5NZW1iZXJJRCA9PT0gbWVtYmVySWQsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIGNvbnN0IHVwZGF0ZWRNZW1iZXJzID0gWy4uLnNpZ25hdHVyZU1lbWJlcnNdXHJcbiAgICAgICAgaWYgKHNpZ25hdHVyZSkge1xyXG4gICAgICAgICAgICBpZiAoaW5kZXggIT09IC0xKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAoc2lnbmF0dXJlLnRyaW0oKSA9PT0gJycpIHtcclxuICAgICAgICAgICAgICAgICAgICB1cGRhdGVkTWVtYmVycy5zcGxpY2UoaW5kZXgsIDEpXHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIHVwZGF0ZWRNZW1iZXJzW2luZGV4XS5TaWduYXR1cmVEYXRhID0gc2lnbmF0dXJlXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB1cGRhdGVkTWVtYmVycy5wdXNoKHtcclxuICAgICAgICAgICAgICAgICAgICBNZW1iZXJJRDogbWVtYmVySWQsXHJcbiAgICAgICAgICAgICAgICAgICAgU2lnbmF0dXJlRGF0YTogc2lnbmF0dXJlLFxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHVwZGF0ZWRNZW1iZXJzLnNwbGljZShpbmRleCwgMSlcclxuICAgICAgICB9XHJcbiAgICAgICAgc2V0U2lnbmF0dXJlTWVtYmVycyh1cGRhdGVkTWVtYmVycylcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIW9mZmxpbmUpIHtcclxuICAgICAgICBnZXRUcmFpbmluZ1R5cGVCeUlEKHRyYWluaW5nVHlwZUlkLCBzZXRUcmFpbmluZylcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVUcmFpbmluZ1Zlc3NlbENoYW5nZSA9ICh2ZXNzZWw6IGFueSkgPT4ge1xyXG4gICAgICAgIHNldFRyYWluaW5nKHtcclxuICAgICAgICAgICAgLi4udHJhaW5pbmcsXHJcbiAgICAgICAgICAgIFZlc3NlbElEOiB2ZXNzZWwudmFsdWUsXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBnZXRDdXJyZW50RXZlbnQgPSBhc3luYyAoaWQ6IGFueSkgPT4ge1xyXG4gICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgIC8vIGdldFRyaXBFdmVudFxyXG4gICAgICAgICAgICBjb25zdCBldmVudCA9IGF3YWl0IHRyaXBFdmVudE1vZGVsLmdldEJ5SWQoaWQpXHJcbiAgICAgICAgICAgIGlmIChldmVudCkge1xyXG4gICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdJRChldmVudC5jcmV3VHJhaW5pbmdJRClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGdldFRyaXBFdmVudCh7XHJcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICBpZDogaWQsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbZ2V0VHJpcEV2ZW50XSA9IHVzZUxhenlRdWVyeShHZXRUcmlwRXZlbnQsIHtcclxuICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGV2ZW50ID0gcmVzcG9uc2UucmVhZE9uZVRyaXBFdmVudFxyXG4gICAgICAgICAgICBpZiAoZXZlbnQpIHtcclxuICAgICAgICAgICAgICAgIHNldFRyYWluaW5nSUQoZXZlbnQuY3Jld1RyYWluaW5nLmlkKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBjdXJyZW50IGV2ZW50JywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcbiAgICBjb25zdCBbcXVlcnlUcmFpbmluZ1Nlc3Npb25CeUlEXSA9IHVzZUxhenlRdWVyeShUUkFJTklOR19TRVNTSU9OX0JZX0lELCB7XHJcbiAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkT25lVHJhaW5pbmdTZXNzaW9uXHJcbiAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICBoYW5kbGVTZXRUcmFpbmluZyhkYXRhKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdxdWVyeVRyYWluaW5nU2Vzc2lvbiBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG4gICAgY29uc3QgbG9hZFRyYWluaW5nU2Vzc2lvbiA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICBpZiAob2ZmbGluZSkge1xyXG4gICAgICAgICAgICAvLyBxdWVyeVRyYWluaW5nU2Vzc2lvbkJ5SURcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHRyYWluaW5nU2Vzc2lvbk1vZGVsLmdldEJ5SWQodHJhaW5pbmdJRClcclxuICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgIGhhbmRsZVNldFRyYWluaW5nKGRhdGEpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBhd2FpdCBxdWVyeVRyYWluaW5nU2Vzc2lvbkJ5SUQoe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6ICt0cmFpbmluZ0lELFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBjb25zdCBoYW5kbGVTdGFydFRpbWVDaGFuZ2UgPSAoZGF0ZTogYW55KSA9PiB7XHJcbiAgICAgICAgc2V0U3RhcnRUaW1lKGRheWpzKGRhdGUpLmZvcm1hdCgnSEg6bW0nKSlcclxuICAgIH1cclxuICAgIGNvbnN0IGhhbmRsZUZpbmlzaFRpbWVDaGFuZ2UgPSAoZGF0ZTogYW55KSA9PiB7XHJcbiAgICAgICAgc2V0RmluaXNoVGltZShkYXlqcyhkYXRlKS5mb3JtYXQoJ0hIOm1tJykpXHJcbiAgICB9XHJcbiAgICBjb25zdCBkaXNwbGF5RmllbGQgPSAoZmllbGROYW1lOiBzdHJpbmcpID0+IHtcclxuICAgICAgICBjb25zdCBldmVudFR5cGVzQ29uZmlnID1cclxuICAgICAgICAgICAgbG9nQm9va0NvbmZpZz8uY3VzdG9taXNlZExvZ0Jvb2tDb21wb25lbnRzPy5ub2Rlcz8uZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgKG5vZGU6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBub2RlLmNvbXBvbmVudENsYXNzID09PSAnRXZlbnRUeXBlX0xvZ0Jvb2tDb21wb25lbnQnLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICBldmVudFR5cGVzQ29uZmlnPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgIGV2ZW50VHlwZXNDb25maWdbMF0/LmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZHM/Lm5vZGVzLmZpbHRlcihcclxuICAgICAgICAgICAgICAgIChmaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIGZpZWxkLmZpZWxkTmFtZSA9PT0gZmllbGROYW1lICYmIGZpZWxkLnN0YXR1cyAhPT0gJ09mZicsXHJcbiAgICAgICAgICAgICkubGVuZ3RoID4gMFxyXG4gICAgICAgICkge1xyXG4gICAgICAgICAgICByZXR1cm4gdHJ1ZVxyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gZmFsc2VcclxuICAgIH1cclxuICAgIGNvbnN0IGhhbmRsZUxvY2F0aW9uQ2hhbmdlID0gKHZhbHVlOiBhbnkpID0+IHtcclxuICAgICAgICAvLyBJZiB2YWx1ZSBpcyBudWxsIG9yIHVuZGVmaW5lZCwgcmV0dXJuIGVhcmx5XHJcbiAgICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuXHJcblxyXG4gICAgICAgIC8vIENoZWNrIGlmIHRoZSB2YWx1ZSBpcyBmcm9tIGRyb3Bkb3duIHNlbGVjdGlvbiAoaGFzICd2YWx1ZScgcHJvcGVydHkpXHJcbiAgICAgICAgaWYgKHZhbHVlLnZhbHVlKSB7XHJcbiAgICAgICAgICAgIC8vIEhhbmRsZSBsb2NhdGlvbiBzZWxlY3RlZCBmcm9tIGRyb3Bkb3duXHJcbiAgICAgICAgICAgIHNldFRyYWluaW5nKHtcclxuICAgICAgICAgICAgICAgIC4uLnRyYWluaW5nLFxyXG4gICAgICAgICAgICAgICAgR2VvTG9jYXRpb25JRDogK3ZhbHVlLnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgTGF0OiBudWxsLFxyXG4gICAgICAgICAgICAgICAgTG9uZzogbnVsbCxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgICAgICB2YWx1ZS5sYXRpdHVkZSAhPT0gdW5kZWZpbmVkICYmXHJcbiAgICAgICAgICAgIHZhbHVlLmxvbmdpdHVkZSAhPT0gdW5kZWZpbmVkXHJcbiAgICAgICAgKSB7XHJcbiAgICAgICAgICAgIC8vIEhhbmRsZSBkaXJlY3QgY29vcmRpbmF0ZXMgaW5wdXRcclxuICAgICAgICAgICAgc2V0VHJhaW5pbmcoe1xyXG4gICAgICAgICAgICAgICAgLi4udHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICBHZW9Mb2NhdGlvbklEOiAwLCAvLyBSZXNldCBnZW9Mb2NhdGlvbklEIHdoZW4gdXNpbmcgZGlyZWN0IGNvb3JkaW5hdGVzXHJcbiAgICAgICAgICAgICAgICBMYXQ6IHZhbHVlLmxhdGl0dWRlLFxyXG4gICAgICAgICAgICAgICAgTG9uZzogdmFsdWUubG9uZ2l0dWRlLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGNvbnN0IGhhbmRsZVNldEN1cnJlbnRMb2NhdGlvbiA9ICh2YWx1ZTogYW55KSA9PiB7XHJcbiAgICAgICAgc2V0VHJhaW5pbmcoe1xyXG4gICAgICAgICAgICAuLi50cmFpbmluZyxcclxuICAgICAgICAgICAgR2VvTG9jYXRpb25JRDogMCxcclxuICAgICAgICAgICAgTGF0OiB2YWx1ZS5sYXRpdHVkZSxcclxuICAgICAgICAgICAgTG9uZzogdmFsdWUubG9uZ2l0dWRlLFxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChzZWxlY3RlZEV2ZW50KSB7XHJcbiAgICAgICAgICAgIHNldEN1cnJlbnRFdmVudChzZWxlY3RlZEV2ZW50KVxyXG4gICAgICAgICAgICBnZXRDdXJyZW50RXZlbnQoc2VsZWN0ZWRFdmVudD8uaWQpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChjdXJyZW50RXZlbnQpIHtcclxuICAgICAgICAgICAgZ2V0Q3VycmVudEV2ZW50KGN1cnJlbnRFdmVudD8uaWQpXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW3NlbGVjdGVkRXZlbnQsIGN1cnJlbnRFdmVudF0pXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmICgrdHJhaW5pbmdJRCA+IDApIHtcclxuICAgICAgICAgICAgbG9hZFRyYWluaW5nU2Vzc2lvbigpXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW3RyYWluaW5nSURdKVxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAoaXNFbXB0eSh0cmFpbmluZykpIHtcclxuICAgICAgICAgICAgc2V0VHJhaW5pbmcoe1xyXG4gICAgICAgICAgICAgICAgLi4udHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICBWZXNzZWxJRDogdmVzc2VsSWQsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW3RyYWluaW5nXSlcclxuICAgIGNvbnN0IG9mZmxpbmVVc2VFZmZlY3QgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgLy8gZ2V0VHJhaW5pbmdUeXBlQnlJRCh0cmFpbmluZ1R5cGVJZCwgc2V0VHJhaW5pbmcpXHJcbiAgICAgICAgY29uc3QgdHJhaW5pbmcgPSBhd2FpdCB0cmFpbmluZ1R5cGVNb2RlbC5nZXRCeUlkKHRyYWluaW5nVHlwZUlkKVxyXG4gICAgICAgIHNldFRyYWluaW5nKHRyYWluaW5nKVxyXG4gICAgICAgIC8vIGdldFRyYWluaW5nVHlwZXMoc2V0VHJhaW5pbmdUeXBlcylcclxuICAgICAgICBjb25zdCB0eXBlcyA9IGF3YWl0IHRyYWluaW5nVHlwZU1vZGVsLmdldEFsbCgpXHJcbiAgICAgICAgc2V0VHJhaW5pbmdUeXBlcyh0eXBlcylcclxuICAgIH1cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgb2ZmbGluZVVzZUVmZmVjdCgpXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW29mZmxpbmVdKVxyXG5cclxuICAgIGNvbnN0IFtjcmVhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhXSA9IHVzZU11dGF0aW9uKFxyXG4gICAgICAgIENSRUFURV9DVVNUT01JU0VEX0NPTVBPTkVOVF9GSUVMRF9EQVRBLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuY3JlYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YVxyXG4gICAgICAgICAgICAgICAgaWYgKGRhdGEuaWQgPiAwICYmIHJhd1RyYWluaW5nPy5wcm9jZWR1cmVGaWVsZHM/Lm5vZGVzKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0UmF3VHJhaW5pbmcoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5yYXdUcmFpbmluZyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGRzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yYXdUcmFpbmluZy5wcm9jZWR1cmVGaWVsZHMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBub2RlczogWy4uLnJhd1RyYWluaW5nLnByb2NlZHVyZUZpZWxkcy5ub2RlcywgZGF0YV0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgJ2NyZWF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGEgZXJyb3InLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXNwb25zZSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdjcmVhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhIGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgIClcclxuXHJcbiAgICBjb25zdCBbdXBkYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YV0gPSB1c2VNdXRhdGlvbihcclxuICAgICAgICBVUERBVEVfQ1VTVE9NSVNFRF9DT01QT05FTlRfRklFTERfREFUQSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnVwZGF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGFcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhLmlkID4gMCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFJhd1RyYWluaW5nKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmF3VHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2NlZHVyZUZpZWxkczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmF3VHJhaW5pbmcucHJvY2VkdXJlRmllbGRzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbm9kZXM6IFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yYXdUcmFpbmluZz8ucHJvY2VkdXJlRmllbGRzPy5ub2Rlcy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZElELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5kYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICd1cGRhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhIGVycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigndXBkYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICApXHJcblxyXG4gICAgY29uc3QgZ2V0UHJvY2VkdXJlcyA9ICgpID0+IHtcclxuICAgICAgICBjb25zdCBwcm9jZWR1cmVzID0gdHJhaW5pbmdUeXBlcy5maWx0ZXIoKHR5cGU6IGFueSkgPT5cclxuICAgICAgICAgICAgdHJhaW5pbmc/LlRyYWluaW5nVHlwZXM/LmluY2x1ZGVzKHR5cGUuaWQpLFxyXG4gICAgICAgIClcclxuICAgICAgICByZXR1cm4gcHJvY2VkdXJlc1xyXG4gICAgICAgICAgICAubWFwKCh0eXBlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIHJldHVybiB0eXBlLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZC5ub2Rlcy5sZW5ndGggPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgPyB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHR5cGUuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHR5cGUudGl0bGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRzOiB0eXBlLmN1c3RvbWlzZWRDb21wb25lbnRGaWVsZC5ub2RlcyxcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICA6IG51bGxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgLmZpbHRlcigodHlwZTogYW55KSA9PiB0eXBlICE9IG51bGwpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlUHJvY2VkdXJlQ2hlY2tzID0gKGZpZWxkOiBhbnksIHR5cGU6IGFueSwgc3RhdHVzOiBib29sZWFuKSA9PiB7XHJcbiAgICAgICAgaWYgKCF0cmFpbmluZ0lEKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHByb2NlZHVyZUNoZWNrID0gYnVmZmVyUHJvY2VkdXJlQ2hlY2suZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgKHByb2NlZHVyZUZpZWxkOiBhbnkpID0+IHByb2NlZHVyZUZpZWxkLmZpZWxkSWQgIT09IGZpZWxkLmlkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIHNldEJ1ZmZlclByb2NlZHVyZUNoZWNrKFtcclxuICAgICAgICAgICAgICAgIC4uLnByb2NlZHVyZUNoZWNrLFxyXG4gICAgICAgICAgICAgICAgeyBmaWVsZElkOiBmaWVsZC5pZCwgc3RhdHVzOiBzdGF0dXMgfSxcclxuICAgICAgICAgICAgXSlcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IG5vZGVzID0gcmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXNcclxuICAgICAgICBjb25zdCBleGlzdGluZ0ZpZWxkID0gbm9kZXM/LmZpbmQoXHJcbiAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgPT09IGZpZWxkLmlkLFxyXG4gICAgICAgIClcclxuICAgICAgICBpZiAoIW5vZGVzIHx8ICFleGlzdGluZ0ZpZWxkKSB7XHJcbiAgICAgICAgICAgIGNyZWF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGEoe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBzdGF0dXMgPyAnT2snIDogJ05vdF9PaycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbklEOiB0cmFpbmluZ0lELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXN0b21pc2VkQ29tcG9uZW50RmllbGRJRDogZmllbGQuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKG5vZGVzLmxlbmd0aCA+IDAgJiYgZXhpc3RpbmdGaWVsZCkge1xyXG4gICAgICAgICAgICBjb25zdCBmaWVsZElEID0gZXhpc3RpbmdGaWVsZC5pZFxyXG4gICAgICAgICAgICB1cGRhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhKHtcclxuICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiArZmllbGRJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBzdGF0dXMgPyAnT2snIDogJ05vdF9PaycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbklEOiB0cmFpbmluZ0lELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXN0b21pc2VkQ29tcG9uZW50RmllbGRJRDogZmllbGQuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY3JlYXRlQ3VzdG9taXNlZENvbXBvbmVudEZpZWxkRGF0YSh7XHJcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHN0YXR1cyA/ICdPaycgOiAnTm90X09rJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uSUQ6IHRyYWluaW5nSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGN1c3RvbWlzZWRDb21wb25lbnRGaWVsZElEOiBmaWVsZC5pZCxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZ2V0RmllbGRTdGF0dXMgPSAoZmllbGQ6IGFueSkgPT4ge1xyXG4gICAgICAgIGlmIChidWZmZXJQcm9jZWR1cmVDaGVjay5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkU3RhdHVzID0gYnVmZmVyUHJvY2VkdXJlQ2hlY2suZmluZChcclxuICAgICAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PiBwcm9jZWR1cmVGaWVsZC5maWVsZElkID09IGZpZWxkLmlkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIGlmIChmaWVsZFN0YXR1cykge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZpZWxkU3RhdHVzLnN0YXR1cyA/ICdPaycgOiAnTm90X09rJ1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGZpZWxkU3RhdHVzID0gcmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXM/LmZpbmQoXHJcbiAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgPT0gZmllbGQuaWQsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHJldHVybiBmaWVsZFN0YXR1cz8uc3RhdHVzIHx8ICcnXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qgc2hvd0NvbW1lbnRQb3B1cCA9IChmaWVsZDogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc3QgZmllbGRDb21tZW50ID0gcmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXM/LmZpbmQoXHJcbiAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgPT0gZmllbGQuaWQsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIGlmIChidWZmZXJGaWVsZENvbW1lbnQubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBjb25zdCBmaWVsZENvbW1lbnQgPSBidWZmZXJGaWVsZENvbW1lbnQuZmluZChcclxuICAgICAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PiBwcm9jZWR1cmVGaWVsZC5maWVsZElkID09IGZpZWxkLmlkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIHNldEN1cnJlbnRDb21tZW50KGZpZWxkQ29tbWVudD8uY29tbWVudCB8fCAnJylcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBzZXRDdXJyZW50Q29tbWVudChmaWVsZENvbW1lbnQ/LmNvbW1lbnQgfHwgJycpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldEN1cnJlbnRGaWVsZChmaWVsZClcclxuICAgICAgICBzZXRDdXJyZW50RmllbGRDb21tZW50KGZpZWxkQ29tbWVudClcclxuICAgICAgICBzZXRPcGVuQ29tbWVudEFsZXJ0KHRydWUpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZ2V0Q29tbWVudCA9IChmaWVsZDogYW55KSA9PiB7XHJcbiAgICAgICAgaWYgKGJ1ZmZlckZpZWxkQ29tbWVudC5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkQ29tbWVudCA9IGJ1ZmZlckZpZWxkQ29tbWVudC5maW5kKFxyXG4gICAgICAgICAgICAgICAgKHByb2NlZHVyZUZpZWxkOiBhbnkpID0+IHByb2NlZHVyZUZpZWxkLmZpZWxkSWQgPT0gZmllbGQuaWQsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICAgaWYgKGZpZWxkQ29tbWVudCkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZpZWxkQ29tbWVudC5jb21tZW50XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgZmllbGRDb21tZW50ID0gcmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXM/LmZpbmQoXHJcbiAgICAgICAgICAgIChwcm9jZWR1cmVGaWVsZDogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgPT0gZmllbGQuaWQsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHJldHVybiBmaWVsZENvbW1lbnQ/LmNvbW1lbnQgfHwgZmllbGQuY29tbWVudFxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNhdmVDb21tZW50ID0gKCkgPT4ge1xyXG4gICAgICAgIGlmICghdHJhaW5pbmdJRCkge1xyXG4gICAgICAgICAgICBjb25zdCBmaWVsZENvbW1lbnQgPSBidWZmZXJGaWVsZENvbW1lbnQuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgKHByb2NlZHVyZUZpZWxkOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuZmllbGRJZCAhPT0gY3VycmVudEZpZWxkLmlkLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIHNldEJ1ZmZlckZpZWxkQ29tbWVudChbXHJcbiAgICAgICAgICAgICAgICAuLi5maWVsZENvbW1lbnQsXHJcbiAgICAgICAgICAgICAgICB7IGZpZWxkSWQ6IGN1cnJlbnRGaWVsZC5pZCwgY29tbWVudDogY3VycmVudENvbW1lbnQgfSxcclxuICAgICAgICAgICAgXSlcclxuICAgICAgICAgICAgc2V0T3BlbkNvbW1lbnRBbGVydChmYWxzZSlcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChjdXJyZW50RmllbGRDb21tZW50KSB7XHJcbiAgICAgICAgICAgIHVwZGF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGEoe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGN1cnJlbnRGaWVsZENvbW1lbnQuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbklEOiB0cmFpbmluZ0lELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXN0b21pc2VkQ29tcG9uZW50RmllbGRJRDogY3VycmVudEZpZWxkLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb21tZW50OiBjdXJyZW50Q29tbWVudCxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IG5vZGVzID0gcmF3VHJhaW5pbmc/LnByb2NlZHVyZUZpZWxkcz8ubm9kZXNcclxuICAgICAgICAgICAgaWYgKG5vZGVzKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRSYXdUcmFpbmluZyh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4ucmF3VHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGRzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnJhd1RyYWluaW5nLnByb2NlZHVyZUZpZWxkcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbm9kZXM6IFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLm5vZGVzLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAocHJvY2VkdXJlRmllbGQ6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2VkdXJlRmllbGQuY3VzdG9taXNlZENvbXBvbmVudEZpZWxkSUQgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRGaWVsZC5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uY3VycmVudEZpZWxkQ29tbWVudCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21tZW50OiBjdXJyZW50Q29tbWVudCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjcmVhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhKHtcclxuICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbklEOiB0cmFpbmluZ0lELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXN0b21pc2VkQ29tcG9uZW50RmllbGRJRDogY3VycmVudEZpZWxkLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb21tZW50OiBjdXJyZW50Q29tbWVudCxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICAgICAgc2V0T3BlbkNvbW1lbnRBbGVydChmYWxzZSlcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgICAgICAgIHshdHJhaW5pbmcgJiYgdHJhaW5pbmdJRCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICA8VHJhaW5pbmdTZXNzaW9uRm9ybVNrZWxldG9uIC8+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBsYWJlbD1cIk5hbWUgb2YgdHJhaW5lclwiIGRpc2FibGVkPXtsb2NrZWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q3Jld0Ryb3Bkb3duXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9ja2VkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb2ZmbGluZT17b2ZmbGluZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0cmFpbmluZz8uVHJhaW5lcklEfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZVRyYWluZXJDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJJZE9wdGlvbnM9e21lbWJlcklkT3B0aW9uc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNtYWxsIGNsYXNzTmFtZT1cInRleHQtZGVzdHJ1Y3RpdmVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNGb3JtRXJyb3JzICYmIGZvcm1FcnJvcnMuVHJhaW5lcklEfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NtYWxsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGxhYmVsPVwiQ3JldyB0cmFpbmVkXCIgZGlzYWJsZWQ9e2xvY2tlZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDcmV3TXVsdGlTZWxlY3REcm9wZG93blxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb2ZmbGluZT17b2ZmbGluZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0cmFpbmluZz8uTWVtYmVyc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVNZW1iZXJDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJJZE9wdGlvbnM9e21lbWJlcklkT3B0aW9uc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgbGFiZWw9XCJUcmFpbmluZyB0eXBlc1wiIGRpc2FibGVkPXtsb2NrZWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc3BhY2UteS02IHctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYWluaW5nVHlwZU11bHRpU2VsZWN0RHJvcGRvd25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvZmZsaW5lPXtvZmZsaW5lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0cmFpbmluZz8uVHJhaW5pbmdUeXBlc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlVHJhaW5pbmdUeXBlQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2tlZD17bG9ja2VkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRXJyb3JzLlRyYWluaW5nVHlwZXMgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzbWFsbCBjbGFzc05hbWU9XCJ0ZXh0LWRlc3RydWN0aXZlIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1FcnJvcnMuVHJhaW5pbmdUeXBlc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NtYWxsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dHJhaW5pbmcgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1R5cGVzLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHR5cGU6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nPy5UcmFpbmluZ1R5cGVzPy5pbmNsdWRlcyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSAmJiB0eXBlLnByb2NlZHVyZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwicHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZpdCBmbGV4LTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRPcGVuVmlld1Byb2NlZHVyZSh0cnVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZpZXcgUHJvY2VkdXJlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBsYWJlbD1cIkxvY2F0aW9uIG9mIHRyYWluaW5nXCIgZGlzYWJsZWQ9e2xvY2tlZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHt2ZXNzZWxMaXN0ICYmIChyYXdUcmFpbmluZyB8fCB0cmFpbmluZ0lEID09PSAwKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29tYm9ib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXt2ZXNzZWxMaXN0IHx8IFtdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmF3VHJhaW5pbmc/LnRyYWluaW5nTG9jYXRpb25UeXBlID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnVmVzc2VsJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB2ZXNzZWxMaXN0Py5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAodmVzc2VsOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgK3Zlc3NlbC52YWx1ZSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICArdHJhaW5pbmc/LlZlc3NlbElELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApWzBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJhd1RyYWluaW5nPy50cmFpbmluZ0xvY2F0aW9uVHlwZSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnT25zaG9yZSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAnRGVza3RvcC9zaG9yZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiByYXdUcmFpbmluZz8udHJhaW5pbmdMb2NhdGlvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJhd1RyYWluaW5nPy50cmFpbmluZ0xvY2F0aW9uVHlwZSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdPdGhlcidcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogJ090aGVyJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogcmF3VHJhaW5pbmc/LnRyYWluaW5nTG9jYXRpb25UeXBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcmF3VHJhaW5pbmc/LnRyYWluaW5nTG9jYXRpb25UeXBlID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ0xvY2F0aW9uJyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByYXdUcmFpbmluZz8udHJhaW5pbmdMb2NhdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPy5pZCA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IHJhd1RyYWluaW5nXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPy50cmFpbmluZ0xvY2F0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPy50aXRsZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiByYXdUcmFpbmluZ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8udHJhaW5pbmdMb2NhdGlvbj8uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdmVzc2VsTGlzdD8uZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh2ZXNzZWw6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICArdmVzc2VsLnZhbHVlID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICt2ZXNzZWxJZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSB8fCBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVUcmFpbmluZ1Zlc3NlbENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBsb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNEaXNhYmxlZD17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBidXR0b25DbGFzc05hbWU9XCJ3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzbWFsbCBjbGFzc05hbWU9XCJ0ZXh0LWRlc3RydWN0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNGb3JtRXJyb3JzICYmIGZvcm1FcnJvcnMuVmVzc2VsSUR9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zbWFsbD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAge2Rpc3BsYXlGaWVsZCgnQ3Jld1RyYWluaW5nX0Z1ZWxMZXZlbCcpICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkZ1ZWwgbGV2ZWwgYXQgZW5kIG9mIHRyYWluaW5nXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2NrZWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJmdWVsLWxldmVsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwiZnVlbC1sZXZlbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlPXt0cmFpbmluZz8uRnVlbExldmVsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbGB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJGdWVsIGxldmVsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17ZGVib3VuY2UoZnVuY3Rpb24gKGUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VHJhaW5pbmcoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udHJhaW5pbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBGdWVsTGV2ZWw6IGUudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sIDYwMCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAge2Rpc3BsYXlGaWVsZCgnQ3Jld1RyYWluaW5nX1N0YXJ0VGltZScpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChkaXNwbGF5RmllbGQoJ0NyZXdUcmFpbmluZ19GaW5pc2hUaW1lJykgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJUcmFpbmluZyBkdXJhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWItMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvY2tlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7ZGlzcGxheUZpZWxkKCdDcmV3VHJhaW5pbmdfU3RhcnRUaW1lJykgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgbGFiZWw9XCJTdGFydCB0aW1lIG9mIHRyYWluaW5nXCIgZGlzYWJsZWQ9e2xvY2tlZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGltZUZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGltZT17c3RhcnRUaW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVRpbWVDaGFuZ2U9e2hhbmRsZVN0YXJ0VGltZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aW1lSUQ9XCJzdGFydFRpbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkTmFtZT1cIlRpbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7ZGlzcGxheUZpZWxkKCdDcmV3VHJhaW5pbmdfRmluaXNoVGltZScpICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGxhYmVsPVwiRW5kIHRpbWUgb2YgdHJhaW5pbmdcIiBkaXNhYmxlZD17bG9ja2VkfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUaW1lRmllbGRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aW1lPXtmaW5pc2hUaW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVRpbWVDaGFuZ2U9e2hhbmRsZUZpbmlzaFRpbWVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGltZUlEPVwiZmluaXNoVGltZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGROYW1lPVwiVGltZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsoIWN1cnJlbnRFdmVudCB8fCB0cmFpbmluZykgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8RWRpdG9yXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cIlRyYWluaW5nU3VtbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlN1bW1hcnkgb2YgdHJhaW5pbmcsIGlkZW50aWZ5IGFueSBvdXRjb21lcywgZnVydGhlciB0cmFpbmluZyByZXF1aXJlZCBvciBvdGhlciBvYnNlcnZhdGlvbnMuXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUVkaXRvckNoYW5nZT17aGFuZGxlRWRpdG9yQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudD17Y29udGVudH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2NrZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICB7Lyo8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBteS00IGZsZXggZmxleC1jb2xcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cIlRyYWluaW5nU3VtbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwiVHJhaW5pbmdTdW1tYXJ5XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU3VtbWFyeSBvZiB0cmFpbmluZywgaWRlbnRpZnkgYW55IG91dGNvbWVzLCBmdXJ0aGVyIHRyYWluaW5nIHJlcXVpcmVkIG9yIG90aGVyIG9ic2VydmF0aW9ucy5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlPXt0cmFpbmluZz8uVHJhaW5pbmdTdW1tYXJ5fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Jyd9PjwvdGV4dGFyZWE+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+Ki99XHJcbiAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkTWVtYmVyTGlzdC5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxIND5QYXJ0aWNpcGFudCBTaWduYXR1cmVzPC9IND5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZE1lbWJlckxpc3QubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnksIGluZGV4OiBudW1iZXIpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTaWduYXR1cmVQYWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXttZW1iZXIubGFiZWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyPXttZW1iZXIubGFiZWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySWQ9e21lbWJlci52YWx1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblNpZ25hdHVyZUNoYW5nZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblNpZ25hdHVyZUNoYW5nZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2lnbmF0dXJlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25hdHVyZURhdGE6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaWduYXR1cmVNZW1iZXJzLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHNpZzogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaWcuTWVtYmVySUQgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk/LlNpZ25hdHVyZURhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBzaWduYXR1cmVNZW1iZXJzLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc2lnOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2lnLk1lbWJlcklEID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKT8uSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NrZWQ9e2xvY2tlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBwYi00IHB0LTQgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImJhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkxlZnQ9e0Fycm93TGVmdH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2Nsb3NlTW9kYWx9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwicHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uTGVmdD17Q2hlY2t9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtsb2NrZWQgPyAoKSA9PiB7fSA6IGhhbmRsZVNhdmV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbXV0YXRpb25DcmVhdGVUcmFpbmluZ1Nlc3Npb25Mb2FkaW5nIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbXV0YXRpb25VcGRhdGVUcmFpbmluZ1Nlc3Npb25Mb2FkaW5nXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3RyYWluaW5nSUQgPT09IDAgPyAnU2F2ZScgOiAnVXBkYXRlJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgIDxTaGVldCBvcGVuPXtvcGVuVmlld1Byb2NlZHVyZX0gb25PcGVuQ2hhbmdlPXtzZXRPcGVuVmlld1Byb2NlZHVyZX0+XHJcbiAgICAgICAgICAgICAgICA8U2hlZXRDb250ZW50IHNpZGU9XCJyaWdodFwiIGNsYXNzTmFtZT1cInctMy80IHNtOm1heC13LW1kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNoZWV0SGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2hlZXRUaXRsZT5Qcm9jZWR1cmVzPC9TaGVldFRpdGxlPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvU2hlZXRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNoZWV0Qm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3RyYWluaW5nICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1R5cGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHR5cGU6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nPy5UcmFpbmluZ1R5cGVzPy5pbmNsdWRlcyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSAmJiB0eXBlLnByb2NlZHVyZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLm1hcCgodHlwZTogYW55KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17dHlwZS5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLW1kIHAtNCBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbGVhZGluZy02IG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dHlwZS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt0eXBlLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9faHRtbDogdHlwZS5wcm9jZWR1cmUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvU2hlZXRCb2R5PlxyXG4gICAgICAgICAgICAgICAgPC9TaGVldENvbnRlbnQ+XHJcbiAgICAgICAgICAgIDwvU2hlZXQ+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IENyZXdUcmFpbmluZ0V2ZW50XHJcbiJdLCJuYW1lcyI6WyJDaGVjayIsIkFycm93TGVmdCIsImRheWpzIiwiZGVib3VuY2UiLCJpc0VtcHR5IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJDcmV3RHJvcGRvd24iLCJUcmFpbmluZ1Nlc3Npb25Gb3JtU2tlbGV0b24iLCJDcmV3TXVsdGlTZWxlY3REcm9wZG93biIsIlNpZ25hdHVyZVBhZCIsIkNSRUFURV9UUkFJTklOR19TRVNTSU9OIiwiVVBEQVRFX1RSQUlOSU5HX1NFU1NJT04iLCJDUkVBVEVfTUVNQkVSX1RSQUlOSU5HX1NJR05BVFVSRSIsIlVQREFURV9NRU1CRVJfVFJBSU5JTkdfU0lHTkFUVVJFIiwiQ1JFQVRFX1RSQUlOSU5HX1NFU1NJT05fRFVFIiwiVVBEQVRFX1RSQUlOSU5HX1NFU1NJT05fRFVFIiwiQ3JlYXRlVHJpcEV2ZW50IiwiVXBkYXRlVHJpcEV2ZW50IiwiQ1JFQVRFX0NVU1RPTUlTRURfQ09NUE9ORU5UX0ZJRUxEX0RBVEEiLCJVUERBVEVfQ1VTVE9NSVNFRF9DT01QT05FTlRfRklFTERfREFUQSIsInVzZU1lZGlhUXVlcnkiLCJHRVRfTUVNQkVSX1RSQUlOSU5HX1NJR05BVFVSRVMiLCJHZXRUcmlwRXZlbnQiLCJSRUFEX09ORV9UUkFJTklOR19TRVNTSU9OX0RVRSIsIlRSQUlOSU5HX1NFU1NJT05fQllfSUQiLCJ1c2VNdXRhdGlvbiIsInVzZUxhenlRdWVyeSIsImdldFRyYWluaW5nVHlwZUJ5SUQiLCJnZXRUcmFpbmluZ1R5cGVzIiwiQ29tYm9ib3giLCJUcmFpbmluZ1R5cGVNdWx0aVNlbGVjdERyb3Bkb3duIiwiVGltZUZpZWxkIiwiRWRpdG9yIiwiVHJhaW5pbmdUeXBlTW9kZWwiLCJNZW1iZXJUcmFpbmluZ19TaWduYXR1cmVNb2RlbCIsIlRyaXBFdmVudE1vZGVsIiwiVHJhaW5pbmdTZXNzaW9uRHVlTW9kZWwiLCJUcmFpbmluZ1Nlc3Npb25Nb2RlbCIsImdlbmVyYXRlVW5pcXVlSWQiLCJJbnB1dCIsIkxhYmVsIiwiQnV0dG9uIiwiU2hlZXQiLCJTaGVldENvbnRlbnQiLCJTaGVldEhlYWRlciIsIlNoZWV0VGl0bGUiLCJTaGVldEJvZHkiLCJINCIsIkNyZXdUcmFpbmluZ0V2ZW50IiwidHJhaW5pbmdUeXBlSWQiLCJ2ZXNzZWxJZCIsInNlbGVjdGVkRXZlbnQiLCJjdXJyZW50VHJpcCIsImNsb3NlTW9kYWwiLCJ1cGRhdGVUcmlwUmVwb3J0IiwidHJpcFJlcG9ydCIsImNyZXdNZW1iZXJzIiwibWFzdGVySUQiLCJsb2dCb29rQ29uZmlnIiwidmVzc2VscyIsImxvY2tlZCIsIm9mZmxpbmUiLCJsb2dCb29rU3RhcnREYXRlIiwicmF3VHJhaW5pbmciLCJ0cmFpbmluZ0lEIiwic2V0VHJhaW5pbmdJRCIsImN1cnJlbnRFdmVudCIsInNldEN1cnJlbnRFdmVudCIsInRyYWluaW5nIiwic2V0VHJhaW5pbmciLCJjb250ZW50Iiwic2V0Q29udGVudCIsInNldFJhd1RyYWluaW5nIiwidHJhaW5pbmdEYXRlIiwic2V0VHJhaW5pbmdEYXRlIiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImlzV2lkZSIsImhhc0Zvcm1FcnJvcnMiLCJzZXRIYXNGb3JtRXJyb3JzIiwic2VsZWN0ZWRNZW1iZXJMaXN0Iiwic2V0U2VsZWN0ZWRNZW1iZXJMaXN0Iiwic2lnbmF0dXJlTWVtYmVycyIsInNldFNpZ25hdHVyZU1lbWJlcnMiLCJ2ZXNzZWxMaXN0Iiwic2V0VmVzc2VsTGlzdCIsInRyYWluaW5nVHlwZXMiLCJzZXRUcmFpbmluZ1R5cGVzIiwib3BlblZpZXdQcm9jZWR1cmUiLCJzZXRPcGVuVmlld1Byb2NlZHVyZSIsIm9wZW5EZXNjcmlwdGlvblBhbmVsIiwic2V0T3BlbkRlc2NyaXB0aW9uUGFuZWwiLCJkZXNjcmlwdGlvblBhbmVsQ29udGVudCIsInNldERlc2NyaXB0aW9uUGFuZWxDb250ZW50IiwiZGVzY3JpcHRpb25QYW5lbEhlYWRpbmciLCJzZXREZXNjcmlwdGlvblBhbmVsSGVhZGluZyIsImJ1ZmZlclByb2NlZHVyZUNoZWNrIiwic2V0QnVmZmVyUHJvY2VkdXJlQ2hlY2siLCJidWZmZXJGaWVsZENvbW1lbnQiLCJzZXRCdWZmZXJGaWVsZENvbW1lbnQiLCJjdXJyZW50Q29tbWVudCIsInNldEN1cnJlbnRDb21tZW50IiwiY3VycmVudEZpZWxkIiwic2V0Q3VycmVudEZpZWxkIiwiY3VycmVudEZpZWxkQ29tbWVudCIsInNldEN1cnJlbnRGaWVsZENvbW1lbnQiLCJvcGVuQ29tbWVudEFsZXJ0Iiwic2V0T3BlbkNvbW1lbnRBbGVydCIsImZvcm1FcnJvcnMiLCJzZXRGb3JtRXJyb3JzIiwiVHJhaW5pbmdUeXBlcyIsIlRyYWluZXJJRCIsIlZlc3NlbElEIiwic3RhcnRUaW1lIiwic2V0U3RhcnRUaW1lIiwiZm9ybWF0IiwiZmluaXNoVGltZSIsInNldEZpbmlzaFRpbWUiLCJtZW1iZXJJZE9wdGlvbnMiLCJBcnJheSIsImlzQXJyYXkiLCJtYXAiLCJtIiwiY3Jld01lbWJlcklEIiwiY3VycmVudExvY2F0aW9uIiwic2V0Q3VycmVudExvY2F0aW9uIiwibGF0aXR1ZGUiLCJsb25naXR1ZGUiLCJ0cmFpbmluZ1R5cGVNb2RlbCIsIm1lbWJlclRyYWluaW5nX1NpZ25hdHVyZU1vZGVsIiwidHJpcEV2ZW50TW9kZWwiLCJ0cmFpbmluZ1Nlc3Npb25EdWVNb2RlbCIsInRyYWluaW5nU2Vzc2lvbk1vZGVsIiwiaGFuZGxlU2V0VHJhaW5pbmciLCJ0IiwidERhdGUiLCJkYXRlIiwidHJhaW5pbmdEYXRhIiwiSUQiLCJNZW1iZXJzIiwibWVtYmVycyIsIm5vZGVzIiwiaWQiLCJ0cmFpbmVyIiwidHJhaW5pbmdTdW1tYXJ5IiwiRnVlbExldmVsIiwiZnVlbExldmVsIiwiR2VvTG9jYXRpb25JRCIsImdlb0xvY2F0aW9uSUQiLCJTdGFydFRpbWUiLCJGaW5pc2hUaW1lIiwiTGF0IiwibGF0IiwiTG9uZyIsImxvbmciLCJnZW9Mb2NhdGlvbiIsImxhYmVsIiwiZmlyc3ROYW1lIiwic3VybmFtZSIsInZhbHVlIiwic2lnbmF0dXJlcyIsInMiLCJNZW1iZXJJRCIsIm1lbWJlciIsIlNpZ25hdHVyZURhdGEiLCJzaWduYXR1cmVEYXRhIiwiaGFuZGxlRWRpdG9yQ2hhbmdlIiwibmV3Q29udGVudCIsImFjdGl2ZVZlc3NlbHMiLCJmaWx0ZXIiLCJ2ZXNzZWwiLCJhcmNoaXZlZCIsImZvcm1hdHRlZERhdGEiLCJ0aXRsZSIsIm11dGF0aW9uQ3JlYXRlVHJhaW5pbmdTZXNzaW9uIiwibG9hZGluZyIsIm11dGF0aW9uQ3JlYXRlVHJhaW5pbmdTZXNzaW9uTG9hZGluZyIsIm9uQ29tcGxldGVkIiwicmVzcG9uc2UiLCJkYXRhIiwiY3JlYXRlVHJhaW5pbmdTZXNzaW9uIiwibGVuZ3RoIiwicHJvY2VkdXJlRmllbGRzIiwicHJvY2VkdXJlRmllbGQiLCJzdGF0dXMiLCJ0cmFpbmluZ1Nlc3Npb25JRCIsImN1c3RvbWlzZWRDb21wb25lbnRGaWVsZElEIiwiZmllbGRJZCIsImNvbW1lbnQiLCJmaW5kIiwiZm9yRWFjaCIsImNyZWF0ZUN1c3RvbWlzZWRDb21wb25lbnRGaWVsZERhdGEiLCJ2YXJpYWJsZXMiLCJpbnB1dCIsInVwZGF0ZVRyYWluaW5nU2Vzc2lvbkR1ZXMiLCJ1cGRhdGVTaWduYXR1cmVzIiwidXBkYXRlVHJpcEV2ZW50IiwiZXZlbnRDYXRlZ29yeSIsImNyZXdUcmFpbmluZ0lEIiwiY29uc29sZSIsImVycm9yIiwib25FcnJvciIsIm11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uIiwibXV0YXRpb25VcGRhdGVUcmFpbmluZ1Nlc3Npb25Mb2FkaW5nIiwidXBkYXRlVHJhaW5pbmdTZXNzaW9uIiwicmVhZE9uZVRyYWluaW5nU2Vzc2lvbkR1ZSIsInJlYWRPbmVUcmFpbmluZ1Nlc3Npb25EdWVMb2FkaW5nIiwiZmV0Y2hQb2xpY3kiLCJnZXRUcmFpbmluZ1Nlc3Npb25EdWVXaXRoVmFyaWFibGVzIiwiYWxsRHVlcyIsImdldEFsbCIsIml0ZW0iLCJtZW1iZXJJRCIsImVxIiwidmVzc2VsSUQiLCJ0cmFpbmluZ1R5cGVJRCIsIm11dGF0aW9uQ3JlYXRlVHJhaW5pbmdTZXNzaW9uRHVlIiwiY3JlYXRlVHJhaW5pbmdTZXNzaW9uRHVlTG9hZGluZyIsIm11dGF0aW9uVXBkYXRlVHJhaW5pbmdTZXNzaW9uRHVlIiwidXBkYXRlVHJhaW5pbmdTZXNzaW9uRHVlTG9hZGluZyIsInRyYWluaW5nU2Vzc2lvbkR1ZXMiLCJ0cmFpbmluZ0luZm8iLCJ0dCIsIm9jY3Vyc0V2ZXJ5IiwibmV3RHVlRGF0ZSIsImFkZCIsInB1c2giLCJkdWVEYXRlIiwidHJhaW5pbmdTZXNzaW9uRHVlV2l0aElEcyIsIlByb21pc2UiLCJhbGwiLCJmcm9tIiwic2F2ZSIsImdldEN1cnJlbnRFdmVudCIsInRyaXAiLCJjcmVhdGVUcmlwRXZlbnQiLCJzYXZlVHJhaW5pbmciLCJqb2luIiwidHJhaW5lcklEIiwidHJhaW5pbmdMb2NhdGlvblR5cGUiLCJoYW5kbGVTYXZlIiwiaGFzRXJyb3JzIiwiZXJyb3JzIiwicHJldkVycm9ycyIsIlRyYWluaW5nTG9jYXRpb25JRCIsImlzVmFsaWQiLCJsb2dCb29rRW50cnlTZWN0aW9uSUQiLCJ0cmlwRXZlbnREYXRhIiwiVHJhaW5pbmdJRCIsInNpZ25hdHVyZSIsImNoZWNrQW5kU2F2ZVNpZ25hdHVyZSIsImFsbFNpZ25hdHVyZXMiLCJxdWVyeUdldE1lbWJlclRyYWluaW5nU2lnbmF0dXJlcyIsImluIiwidGhlbiIsInJlYWRNZW1iZXJUcmFpbmluZ19TaWduYXR1cmVzIiwibXV0YXRpb25VcGRhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZSIsIm11dGF0aW9uQ3JlYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmUiLCJjYXRjaCIsIm11dGF0aW9uVXBkYXRlTWVtYmVyVHJhaW5pbmdTaWduYXR1cmVMb2FkaW5nIiwidXBkYXRlTWVtYmVyVHJhaW5pbmdfU2lnbmF0dXJlIiwibXV0YXRpb25DcmVhdGVNZW1iZXJUcmFpbmluZ1NpZ25hdHVyZUxvYWRpbmciLCJjcmVhdGVNZW1iZXJUcmFpbmluZ19TaWduYXR1cmUiLCJoYW5kbGVUcmFpbmluZ0RhdGVDaGFuZ2UiLCJ0b1N0cmluZyIsImhhbmRsZVRyYWluZXJDaGFuZ2UiLCJ0cmFpbmVyVmFsdWUiLCJtZW1iZXJzU2V0IiwiU2V0IiwidHJhaW5lck9iamVjdCIsImlzVHJhaW5lckFscmVhZHlTZWxlY3RlZCIsInNvbWUiLCJoYW5kbGVUcmFpbmluZ1R5cGVDaGFuZ2UiLCJzZWxlY3RlZFR5cGVzIiwiaGFuZGxlTWVtYmVyQ2hhbmdlIiwibWVtYmVyc0FycmF5IiwiQm9vbGVhbiIsIm1lbWJlclZhbHVlcyIsIm9uU2lnbmF0dXJlQ2hhbmdlZCIsIm1lbWJlcklkIiwiaW5kZXgiLCJmaW5kSW5kZXgiLCJvYmplY3QiLCJ1cGRhdGVkTWVtYmVycyIsInRyaW0iLCJzcGxpY2UiLCJoYW5kbGVUcmFpbmluZ1Zlc3NlbENoYW5nZSIsImV2ZW50IiwiZ2V0QnlJZCIsImdldFRyaXBFdmVudCIsInJlYWRPbmVUcmlwRXZlbnQiLCJjcmV3VHJhaW5pbmciLCJxdWVyeVRyYWluaW5nU2Vzc2lvbkJ5SUQiLCJyZWFkT25lVHJhaW5pbmdTZXNzaW9uIiwibG9hZFRyYWluaW5nU2Vzc2lvbiIsImhhbmRsZVN0YXJ0VGltZUNoYW5nZSIsImhhbmRsZUZpbmlzaFRpbWVDaGFuZ2UiLCJkaXNwbGF5RmllbGQiLCJmaWVsZE5hbWUiLCJldmVudFR5cGVzQ29uZmlnIiwiY3VzdG9taXNlZExvZ0Jvb2tDb21wb25lbnRzIiwibm9kZSIsImNvbXBvbmVudENsYXNzIiwiY3VzdG9taXNlZENvbXBvbmVudEZpZWxkcyIsImZpZWxkIiwiaGFuZGxlTG9jYXRpb25DaGFuZ2UiLCJ1bmRlZmluZWQiLCJoYW5kbGVTZXRDdXJyZW50TG9jYXRpb24iLCJvZmZsaW5lVXNlRWZmZWN0IiwidHlwZXMiLCJ1cGRhdGVDdXN0b21pc2VkQ29tcG9uZW50RmllbGREYXRhIiwiZ2V0UHJvY2VkdXJlcyIsInByb2NlZHVyZXMiLCJ0eXBlIiwiaW5jbHVkZXMiLCJjdXN0b21pc2VkQ29tcG9uZW50RmllbGQiLCJmaWVsZHMiLCJoYW5kbGVQcm9jZWR1cmVDaGVja3MiLCJwcm9jZWR1cmVDaGVjayIsImV4aXN0aW5nRmllbGQiLCJmaWVsZElEIiwiZ2V0RmllbGRTdGF0dXMiLCJmaWVsZFN0YXR1cyIsInNob3dDb21tZW50UG9wdXAiLCJmaWVsZENvbW1lbnQiLCJnZXRDb21tZW50IiwiaGFuZGxlU2F2ZUNvbW1lbnQiLCJkaXYiLCJjbGFzc05hbWUiLCJkaXNhYmxlZCIsIm9uQ2hhbmdlIiwic21hbGwiLCJwcm9jZWR1cmUiLCJ2YXJpYW50Iiwib25DbGljayIsIm9wdGlvbnMiLCJ0cmFpbmluZ0xvY2F0aW9uIiwicGxhY2Vob2xkZXIiLCJpc0Rpc2FibGVkIiwiYnV0dG9uQ2xhc3NOYW1lIiwibmFtZSIsImRlZmF1bHRWYWx1ZSIsImUiLCJ0YXJnZXQiLCJ0aW1lIiwiaGFuZGxlVGltZUNoYW5nZSIsInRpbWVJRCIsInNpZyIsImljb25MZWZ0Iiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsInNpZGUiLCJoMyIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\n"));

/***/ })

});