"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/logbook/forms/crew-training-event.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/filter/components/crew-dropdown/crew-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _crew_training_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../crew-training/type-multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/models/trainingType */ \"(app-pages-browser)/./src/app/offline/models/trainingType.js\");\n/* harmony import */ var _app_offline_models_memberTraining_Signature__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/offline/models/memberTraining_Signature */ \"(app-pages-browser)/./src/app/offline/models/memberTraining_Signature.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_trainingSessionDue__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/trainingSessionDue */ \"(app-pages-browser)/./src/app/offline/models/trainingSessionDue.js\");\n/* harmony import */ var _app_offline_models_trainingSession__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/trainingSession */ \"(app-pages-browser)/./src/app/offline/models/trainingSession.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingEvent = (param)=>{\n    let { trainingTypeId = 0, vesselId = 0, selectedEvent = false, currentTrip = false, closeModal, updateTripReport, tripReport, crewMembers, masterID, logBookConfig, vessels, locked, offline = false, logBookStartDate } = param;\n    var _rawTraining_trainingLocation, _rawTraining_trainingLocation1, _rawTraining_trainingLocation2;\n    _s();\n    const [trainingID, setTrainingID] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(selectedEvent);\n    const [training, setTraining] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({});\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [rawTraining, setRawTraining] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingDate, setTrainingDate] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Date().toLocaleDateString());\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery)(\"(min-width: 640px)\");\n    const [hasFormErrors, setHasFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [selectedMemberList, setSelectedMemberList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [signatureMembers, setSignatureMembers] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [vesselList, setVesselList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingTypes, setTrainingTypes] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [openViewProcedure, setOpenViewProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [openDescriptionPanel, setOpenDescriptionPanel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [descriptionPanelContent, setDescriptionPanelContent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [descriptionPanelHeading, setDescriptionPanelHeading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [bufferProcedureCheck, setBufferProcedureCheck] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [bufferFieldComment, setBufferFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentComment, setCurrentComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [currentField, setCurrentField] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [currentFieldComment, setCurrentFieldComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [openCommentAlert, setOpenCommentAlert] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        TrainingTypes: \"\",\n        TrainerID: \"\",\n        VesselID: \"\",\n        Date: \"\"\n    });\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const [finishTime, setFinishTime] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const memberIdOptions = [\n        masterID,\n        ...Array.isArray(crewMembers) ? crewMembers.map((m)=>m.crewMemberID) : []\n    ];\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const trainingTypeModel = new _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_16__[\"default\"]();\n    const memberTraining_SignatureModel = new _app_offline_models_memberTraining_Signature__WEBPACK_IMPORTED_MODULE_17__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const trainingSessionDueModel = new _app_offline_models_trainingSessionDue__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const trainingSessionModel = new _app_offline_models_trainingSession__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypes)(setTrainingTypes);\n    }\n    const handleSetTraining = (t)=>{\n        const tDate = new Date(t.date).toLocaleDateString();\n        setTrainingDate(tDate);\n        const trainingData = {\n            ID: trainingID,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(t.date).format(\"YYYY-MM-DD\"),\n            Members: t.members.nodes.map((m)=>m.id),\n            TrainerID: t.trainer.id,\n            trainingSummary: t.trainingSummary,\n            TrainingTypes: t.trainingTypes.nodes.map((t)=>t.id),\n            VesselID: vesselId,\n            FuelLevel: t.fuelLevel || 0,\n            GeoLocationID: t.geoLocationID,\n            StartTime: t.startTime,\n            FinishTime: t.finishTime,\n            Lat: t.lat,\n            Long: t.long\n        };\n        setContent(t.trainingSummary);\n        setStartTime(t.startTime);\n        setFinishTime(t.finishTime);\n        setRawTraining(t);\n        setTraining(trainingData);\n        if (+t.geoLocationID > 0) {\n            setCurrentLocation({\n                latitude: t.geoLocation.lat,\n                longitude: t.geoLocation.long\n            });\n        } else {\n            setCurrentLocation({\n                latitude: t.lat,\n                longitude: t.long\n            });\n        }\n        const members = t.members.nodes.map((m)=>{\n            var _m_firstName, _m_surname;\n            return {\n                label: \"\".concat((_m_firstName = m.firstName) !== null && _m_firstName !== void 0 ? _m_firstName : \"\", \" \").concat((_m_surname = m.surname) !== null && _m_surname !== void 0 ? _m_surname : \"\"),\n                value: m.id\n            };\n        }) || [];\n        // Include trainer in the member list for signatures\n        const trainer = t.trainer;\n        var _trainer_firstName, _trainer_surname;\n        const trainerMember = {\n            label: \"\".concat((_trainer_firstName = trainer.firstName) !== null && _trainer_firstName !== void 0 ? _trainer_firstName : \"\", \" \").concat((_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"),\n            value: trainer.id\n        };\n        // Combine members and trainer, removing duplicates\n        const allMembers = [\n            ...members\n        ];\n        const isTrainerAlreadyInMembers = members.some((m)=>+m.value === +trainer.id);\n        if (!isTrainerAlreadyInMembers) {\n            allMembers.push(trainerMember);\n        }\n        console.log(\"Setting selectedMemberList with trainer included:\", allMembers);\n        setSelectedMemberList(allMembers);\n        const signatures = t.signatures.nodes.map((s)=>({\n                MemberID: s.member.id,\n                SignatureData: s.signatureData,\n                ID: s.id\n            }));\n        setSignatureMembers(signatures);\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    // const handleSetVessels = (data: any) => {\n    //     const activeVessels = data?.filter((vessel: any) => !vessel.archived)\n    //     const formattedData = [\n    //         {\n    //             label: 'Other',\n    //             value: 'Other',\n    //         },\n    //         {\n    //             label: 'Desktop/shore',\n    //             value: 'Onshore',\n    //         },\n    //         ...activeVessels.map((vessel: any) => ({\n    //             value: vessel.id,\n    //             label: vessel.title,\n    //         })),\n    //     ]\n    //     setVessels(formattedData)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (vessels) {\n            const activeVessels = vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>!vessel.archived);\n            const formattedData = [\n                {\n                    label: \"Other\",\n                    value: \"Other\"\n                },\n                {\n                    label: \"Desktop/shore\",\n                    value: \"Onshore\"\n                },\n                ...activeVessels.map((vessel)=>({\n                        value: vessel.id,\n                        label: vessel.title\n                    }))\n            ];\n            setVesselList(formattedData);\n        }\n    }, [\n        vessels\n    ]);\n    // const [queryVessels] = useLazyQuery(VESSEL_LIST, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (queryVesselResponse: any) => {\n    //         if (queryVesselResponse.readVessels.nodes) {\n    //             handleSetVessels(queryVesselResponse.readVessels.nodes)\n    //         }\n    //     },\n    //     onError: (error: any) => {\n    //         console.error('queryVessels error', error)\n    //     },\n    // })\n    const [mutationCreateTrainingSession, { loading: mutationCreateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.createTrainingSession;\n            if (data.id > 0) {\n                if (bufferProcedureCheck.length > 0) {\n                    const procedureFields = bufferProcedureCheck.map((procedureField)=>{\n                        var _bufferFieldComment_find;\n                        return {\n                            status: procedureField.status ? \"Ok\" : \"Not_Ok\",\n                            trainingSessionID: data.id,\n                            customisedComponentFieldID: procedureField.fieldId,\n                            comment: (_bufferFieldComment_find = bufferFieldComment.find((comment)=>comment.fieldId == procedureField.fieldId)) === null || _bufferFieldComment_find === void 0 ? void 0 : _bufferFieldComment_find.comment\n                        };\n                    });\n                    procedureFields.forEach((procedureField)=>{\n                        createCustomisedComponentFieldData({\n                            variables: {\n                                input: procedureField\n                            }\n                        });\n                    });\n                }\n                setTrainingID(data.id);\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id),\n                            eventCategory: \"CrewTraining\",\n                            crewTrainingID: data.id\n                        }\n                    }\n                });\n                closeModal();\n            } else {\n                console.error(\"mutationCreateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateTrainingSession error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSession, { loading: mutationUpdateTrainingSessionLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_TRAINING_SESSION, {\n        onCompleted: (response)=>{\n            const data = response.updateTrainingSession;\n            if (data.id > 0) {\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n            } else {\n                console.error(\"mutationUpdateTrainingSession error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateTrainingSession error\", error);\n        }\n    });\n    const [readOneTrainingSessionDue, { loading: readOneTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.READ_ONE_TRAINING_SESSION_DUE, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            return response.readOneTrainingSessionDue.data;\n        },\n        onError: (error)=>{\n            console.error(\"readOneTrainingSessionDueLoading error:\", error);\n            return null;\n        }\n    });\n    const getTrainingSessionDueWithVariables = async function() {\n        let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, onCompleted = arguments.length > 1 ? arguments[1] : void 0;\n        if (offline) {\n            const allDues = await trainingSessionDueModel.getAll();\n            const data = allDues.filter((item)=>item.memberID === variables.filter.memberID.eq && item.vesselID === variables.filter.vesselID.eq && item.trainingTypeID === variables.filter.trainingTypeID.eq);\n            onCompleted(data);\n        } else {\n            const { data } = await readOneTrainingSessionDue({\n                variables: variables\n            });\n            onCompleted(data.readOneTrainingSessionDue);\n        }\n    };\n    const [mutationCreateTrainingSessionDue, { loading: createTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"createTrainingSessionDue error\", error);\n        }\n    });\n    const [mutationUpdateTrainingSessionDue, { loading: updateTrainingSessionDueLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_TRAINING_SESSION_DUE, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"updateTrainingSessionDue error\", error);\n        }\n    });\n    const updateTrainingSessionDues = async ()=>{\n        const trainingSessionDues = [];\n        const vesselID = training.VesselID;\n        training.TrainingTypes.forEach((t)=>{\n            const trainingInfo = trainingTypes.find((tt)=>tt.id === t);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingInfo) && trainingInfo.occursEvery > 0) {\n                const trainingTypeID = t;\n                const newDueDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).add(trainingInfo.occursEvery, \"day\");\n                training.Members.forEach((m)=>{\n                    const memberID = m;\n                    trainingSessionDues.push({\n                        dueDate: newDueDate.format(\"YYYY-MM-DD\"),\n                        memberID: memberID,\n                        vesselID: vesselID,\n                        trainingTypeID: trainingTypeID\n                    });\n                });\n            }\n        });\n        let trainingSessionDueWithIDs = [];\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingSessionDues)) {\n            await Promise.all(trainingSessionDues.map(async (item)=>{\n                const variables = {\n                    filter: {\n                        memberID: {\n                            eq: item.memberID\n                        },\n                        vesselID: {\n                            eq: item.vesselID\n                        },\n                        trainingTypeID: {\n                            eq: item.trainingTypeID\n                        }\n                    }\n                };\n                const onCompleted = (response)=>{\n                    var _response_id;\n                    trainingSessionDueWithIDs.push({\n                        ...item,\n                        id: (_response_id = response === null || response === void 0 ? void 0 : response.id) !== null && _response_id !== void 0 ? _response_id : 0\n                    });\n                };\n                await getTrainingSessionDueWithVariables(variables, onCompleted);\n            }));\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingSessionDueWithIDs)) {\n            await Promise.all(Array.from(trainingSessionDueWithIDs).map(async (item)=>{\n                const variables = {\n                    variables: {\n                        input: item\n                    }\n                };\n                if (item.id === 0) {\n                    if (offline) {\n                        // mutationCreateTrainingSessionDue\n                        await trainingSessionDueModel.save({\n                            ...item,\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)()\n                        });\n                    } else {\n                        await mutationCreateTrainingSessionDue(variables);\n                    }\n                } else {\n                    if (offline) {\n                        // mutationUpdateTrainingSessionDue\n                        await trainingSessionDueModel.save(item);\n                    } else {\n                        await mutationUpdateTrainingSessionDue(variables);\n                    }\n                }\n            }));\n        }\n    };\n    const [updateTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UpdateTripEvent, {\n        onCompleted: (response)=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating trip event\", error);\n        }\n    });\n    const [createTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CreateTripEvent, {\n        onCompleted: (response)=>{\n            const data = response.createTripEvent;\n            setCurrentEvent(data);\n            saveTraining();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip event\", error);\n        }\n    });\n    const saveTraining = async ()=>{\n        var _training_Members, _training_TrainingTypes;\n        const input = {\n            id: trainingID,\n            date: training.Date ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(logBookStartDate).format(\"YYYY-MM-DD\") : \"\",\n            members: (_training_Members = training.Members) === null || _training_Members === void 0 ? void 0 : _training_Members.join(\",\"),\n            trainerID: training.TrainerID,\n            trainingSummary: content,\n            trainingTypes: (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.join(\",\"),\n            vesselID: training === null || training === void 0 ? void 0 : training.VesselID,\n            trainingLocationType: (training === null || training === void 0 ? void 0 : training.VesselID) ? training.VesselID === \"Other\" || training.VesselID === \"Onshore\" ? training.VesselID : \"Vessel\" : \"Location\",\n            fuelLevel: \"\".concat(training.FuelLevel),\n            geoLocationID: training.GeoLocationID,\n            startTime: startTime,\n            finishTime: finishTime,\n            lat: \"\".concat(training.Lat),\n            long: \"\".concat(training.Long)\n        };\n        if (trainingID === 0) {\n            if (offline) {\n                // mutationCreateTrainingSession\n                const data = await trainingSessionModel.save({\n                    ...input,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)()\n                });\n                setTrainingID(data.id);\n                updateTrainingSessionDues();\n                updateSignatures(data.id);\n                handleEditorChange(data.trainingSummary);\n                // updateTripEvent\n                await tripEventModel.save({\n                    id: +(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id),\n                    eventCategory: \"CrewTraining\",\n                    crewTrainingID: data.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                await mutationCreateTrainingSession({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // mutationUpdateTrainingSession\n                const data = await trainingSessionModel.save(input);\n                updateTrainingSessionDues();\n                updateSignatures(trainingID);\n                handleEditorChange(data.trainingSummary);\n            } else {\n                await mutationUpdateTrainingSession({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        let hasErrors = false;\n        let errors = {\n            TrainingTypes: \"\",\n            TrainerID: \"\",\n            VesselID: \"\",\n            Date: \"\"\n        };\n        setFormErrors(errors);\n        // Validate Training Types - check if empty or undefined\n        if (!training.TrainingTypes || lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(training.TrainingTypes)) {\n            hasErrors = true;\n            errors.TrainingTypes = \"Nature of training is required\";\n            // Clear any previous error state for this field\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"Nature of training is required\"\n                }));\n        } else {\n            // Clear any previous error for this field when valid\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"\"\n                }));\n        }\n        if (!(training.TrainerID && training.TrainerID > 0)) {\n            hasErrors = true;\n            errors.TrainerID = \"Trainer is required\";\n        }\n        if (!training.VesselID && !(training.TrainingLocationID && training.TrainingLocationID >= 0)) {\n            hasErrors = true;\n            errors.VesselID = \"Location is required\";\n        }\n        if (typeof training.Date === \"undefined\") {\n            training.Date = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(logBookStartDate).format(\"YYYY-MM-DD\");\n        }\n        if (training.Date === null || !dayjs__WEBPACK_IMPORTED_MODULE_1___default()(training.Date).isValid()) {\n            hasErrors = true;\n            errors.Date = \"The date is invalid\";\n        }\n        if (hasErrors) {\n            setHasFormErrors(true);\n            setFormErrors(errors);\n            return;\n        }\n        if (currentEvent) {\n            if (offline) {\n                // updateTripEvent\n                await tripEventModel.save({\n                    id: +currentEvent.id,\n                    eventCategory: \"CrewTraining\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +currentEvent.id,\n                            eventCategory: \"CrewTraining\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n            saveTraining();\n            closeModal();\n        } else {\n            if (offline) {\n                // createTripEvent\n                const tripEventData = await tripEventModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)(),\n                    eventCategory: \"CrewTraining\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                setCurrentEvent(tripEventData);\n                saveTraining();\n            } else {\n                createTripEvent({\n                    variables: {\n                        input: {\n                            eventCategory: \"CrewTraining\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const updateSignatures = (TrainingID)=>{\n        signatureMembers.length > 0 && (signatureMembers === null || signatureMembers === void 0 ? void 0 : signatureMembers.forEach((signature)=>{\n            checkAndSaveSignature(signature, TrainingID);\n        }));\n    };\n    const checkAndSaveSignature = async (signature, TrainingID)=>{\n        if (offline) {\n            // queryGetMemberTrainingSignatures\n            const allSignatures = await memberTraining_SignatureModel.getAll();\n            const data = allSignatures.filter((item)=>item.memberID === signature.MemberID && item.trainingSessionID === TrainingID);\n            if (data.length > 0) {\n                // mutationUpdateMemberTrainingSignature\n                await memberTraining_SignatureModel.save({\n                    id: data[0].id,\n                    memberID: signature.MemberID,\n                    signatureData: signature.SignatureData,\n                    trainingSessionID: TrainingID\n                });\n            } else {\n                // mutationCreateMemberTrainingSignature\n                await memberTraining_SignatureModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_21__.generateUniqueId)(),\n                    memberID: signature.MemberID,\n                    signatureData: signature.SignatureData,\n                    trainingSessionID: TrainingID\n                });\n            }\n        } else {\n            await queryGetMemberTrainingSignatures({\n                variables: {\n                    filter: {\n                        memberID: {\n                            eq: signature.MemberID\n                        },\n                        trainingSessionID: {\n                            in: TrainingID\n                        }\n                    }\n                }\n            }).then((response)=>{\n                const data = response.data.readMemberTraining_Signatures.nodes;\n                if (data.length > 0) {\n                    mutationUpdateMemberTrainingSignature({\n                        variables: {\n                            input: {\n                                id: data[0].id,\n                                memberID: signature.MemberID,\n                                signatureData: signature.SignatureData,\n                                trainingSessionID: TrainingID\n                            }\n                        }\n                    });\n                } else {\n                    if (signature.SignatureData) {\n                        mutationCreateMemberTrainingSignature({\n                            variables: {\n                                input: {\n                                    memberID: signature.MemberID,\n                                    signatureData: signature.SignatureData,\n                                    trainingSessionID: TrainingID\n                                }\n                            }\n                        });\n                    }\n                }\n            }).catch((error)=>{\n                console.error(\"mutationGetMemberTrainingSignatures error\", error);\n            });\n        }\n    };\n    const [queryGetMemberTrainingSignatures] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.GET_MEMBER_TRAINING_SIGNATURES);\n    const [mutationUpdateMemberTrainingSignature, { loading: mutationUpdateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.updateMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationUpdateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationUpdateMemberTrainingSignature error\", error);\n        }\n    });\n    const [mutationCreateMemberTrainingSignature, { loading: mutationCreateMemberTrainingSignatureLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_MEMBER_TRAINING_SIGNATURE, {\n        onCompleted: (response)=>{\n            const data = response.createMemberTraining_Signature;\n            if (data.id > 0) {\n            // signatureCount++\n            // if (signatureCount === signatureMembers.length) {\n            // }\n            } else {\n                console.error(\"mutationCreateMemberTrainingSignature error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationCreateMemberTrainingSignature error\", error);\n        }\n    });\n    const handleTrainingDateChange = (date)=>{\n        setTrainingDate(new Date(date.toString()).toLocaleDateString());\n        setTraining({\n            ...training,\n            Date: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"YYYY-MM-DD\")\n        });\n    };\n    const handleTrainerChange = (trainer)=>{\n        if (!trainer) return; // Add early return if trainer is null\n        // Check if trainer is an array (multiple selection) or a single object\n        const trainerValue = Array.isArray(trainer) ? trainer.length > 0 ? trainer[0].value : null : trainer.value;\n        if (!trainerValue) {\n            return;\n        }\n        // Use Set() to prevent duplicate values, then Array.from() to convert it to an array\n        const membersSet = new Set((training === null || training === void 0 ? void 0 : training.Members) || []);\n        membersSet.add(trainerValue);\n        const members = Array.from(membersSet);\n        setTraining({\n            ...training,\n            TrainerID: trainerValue,\n            Members: members\n        });\n        // Create a proper trainer object for the selectedMemberList\n        const trainerObject = Array.isArray(trainer) ? trainer[0] : trainer;\n        // Check if trainer is already in selectedMemberList to avoid duplicates\n        const isTrainerAlreadySelected = selectedMemberList.some((member)=>+member.value === +trainerValue);\n        if (!isTrainerAlreadySelected) {\n            setSelectedMemberList([\n                ...selectedMemberList,\n                trainerObject\n            ]);\n            setSignatureMembers([\n                ...signatureMembers,\n                {\n                    MemberID: +trainerValue,\n                    SignatureData: null\n                }\n            ]);\n        }\n    };\n    const handleTrainingTypeChange = (trainingTypes)=>{\n        // Update training state with selected types\n        const selectedTypes = !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(trainingTypes) ? trainingTypes.map((item)=>item.value) : [];\n        setTraining({\n            ...training,\n            TrainingTypes: selectedTypes\n        });\n        // Clear error message if valid selection is made\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(selectedTypes)) {\n            setFormErrors((prevErrors)=>({\n                    ...prevErrors,\n                    TrainingTypes: \"\"\n                }));\n        }\n    };\n    const handleMemberChange = (members)=>{\n        // Ensure members is an array\n        const membersArray = Array.isArray(members) ? members : [\n            members\n        ].filter(Boolean);\n        // Make sure we're filtering with valid member values\n        const signatures = signatureMembers.filter((item)=>membersArray.some((m)=>m && m.value && +m.value === item.MemberID));\n        // Extract member values safely\n        const memberValues = membersArray.filter((item)=>item && item.value).map((item)=>item.value);\n        setTraining({\n            ...training,\n            Members: memberValues\n        });\n        setSelectedMemberList(membersArray);\n        setSignatureMembers(signatures);\n    };\n    const onSignatureChanged = (signature, member, memberId)=>{\n        const index = signatureMembers.findIndex((object)=>object.MemberID === memberId);\n        const updatedMembers = [\n            ...signatureMembers\n        ];\n        if (signature) {\n            if (index !== -1) {\n                if (signature.trim() === \"\") {\n                    updatedMembers.splice(index, 1);\n                } else {\n                    updatedMembers[index].SignatureData = signature;\n                }\n            } else {\n                updatedMembers.push({\n                    MemberID: memberId,\n                    SignatureData: signature\n                });\n            }\n        } else {\n            updatedMembers.splice(index, 1);\n        }\n        setSignatureMembers(updatedMembers);\n    };\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_11__.getTrainingTypeByID)(trainingTypeId, setTraining);\n    }\n    const handleTrainingVesselChange = (vessel)=>{\n        setTraining({\n            ...training,\n            VesselID: vessel.value\n        });\n    };\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            // getTripEvent\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                setTrainingID(event.crewTrainingID);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                setTrainingID(event.crewTraining.id);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_10__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                handleSetTraining(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    const loadTrainingSession = async ()=>{\n        if (offline) {\n            // queryTrainingSessionByID\n            const data = await trainingSessionModel.getById(trainingID);\n            if (data) {\n                handleSetTraining(data);\n            }\n        } else {\n            await queryTrainingSessionByID({\n                variables: {\n                    id: +trainingID\n                }\n            });\n        }\n    };\n    const handleStartTimeChange = (date)=>{\n        setStartTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const handleFinishTimeChange = (date)=>{\n        setFinishTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0 && ((_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTraining({\n                ...training,\n                GeoLocationID: +value.value,\n                Lat: null,\n                Long: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTraining({\n                ...training,\n                GeoLocationID: 0,\n                Lat: value.latitude,\n                Long: value.longitude\n            });\n        }\n    };\n    const handleSetCurrentLocation = (value)=>{\n        setTraining({\n            ...training,\n            GeoLocationID: 0,\n            Lat: value.latitude,\n            Long: value.longitude\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        selectedEvent,\n        currentEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (+trainingID > 0) {\n            loadTrainingSession();\n        }\n    }, [\n        trainingID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(training)) {\n            setTraining({\n                ...training,\n                VesselID: vesselId\n            });\n        }\n    }, [\n        training\n    ]);\n    const offlineUseEffect = async ()=>{\n        // getTrainingTypeByID(trainingTypeId, setTraining)\n        const training = await trainingTypeModel.getById(trainingTypeId);\n        setTraining(training);\n        // getTrainingTypes(setTrainingTypes)\n        const types = await trainingTypeModel.getAll();\n        setTrainingTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (offline) {\n            offlineUseEffect();\n        }\n    }, [\n        offline\n    ]);\n    const [createCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            var _rawTraining_procedureFields;\n            const data = response.createCustomisedComponentFieldData;\n            if (data.id > 0 && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes)) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining.procedureFields.nodes,\n                            data\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"createCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createCustomisedComponentFieldData error\", error);\n        }\n    });\n    const [updateCustomisedComponentFieldData] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.UPDATE_CUSTOMISED_COMPONENT_FIELD_DATA, {\n        onCompleted: (response)=>{\n            const data = response.updateCustomisedComponentFieldData;\n            if (data.id > 0) {\n                var _rawTraining_procedureFields;\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== data.customisedComponentFieldID),\n                            {\n                                ...data\n                            }\n                        ]\n                    }\n                });\n            } else {\n                console.error(\"updateCustomisedComponentFieldData error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"updateCustomisedComponentFieldData error\", error);\n        }\n    });\n    const getProcedures = ()=>{\n        const procedures = trainingTypes.filter((type)=>{\n            var _training_TrainingTypes;\n            return training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id);\n        });\n        return procedures.map((type)=>{\n            return type.customisedComponentField.nodes.length > 0 ? {\n                id: type.id,\n                title: type.title,\n                fields: type.customisedComponentField.nodes\n            } : null;\n        }).filter((type)=>type != null);\n    };\n    const handleProcedureChecks = (field, type, status)=>{\n        var _rawTraining_procedureFields;\n        if (!trainingID) {\n            const procedureCheck = bufferProcedureCheck.filter((procedureField)=>procedureField.fieldId !== field.id);\n            setBufferProcedureCheck([\n                ...procedureCheck,\n                {\n                    fieldId: field.id,\n                    status: status\n                }\n            ]);\n            return;\n        }\n        const nodes = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes;\n        const existingField = nodes === null || nodes === void 0 ? void 0 : nodes.find((procedureField)=>procedureField.customisedComponentFieldID === field.id);\n        if (!nodes || !existingField) {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n            return;\n        }\n        if (nodes.length > 0 && existingField) {\n            const fieldID = existingField.id;\n            updateCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        id: +fieldID,\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n        } else {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        status: status ? \"Ok\" : \"Not_Ok\",\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: field.id\n                    }\n                }\n            });\n        }\n    };\n    const getFieldStatus = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferProcedureCheck.length > 0) {\n            const fieldStatus = bufferProcedureCheck.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldStatus) {\n                return fieldStatus.status ? \"Ok\" : \"Not_Ok\";\n            }\n        }\n        const fieldStatus = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldStatus === null || fieldStatus === void 0 ? void 0 : fieldStatus.status) || \"\";\n    };\n    const showCommentPopup = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        } else {\n            setCurrentComment((fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || \"\");\n        }\n        setCurrentField(field);\n        setCurrentFieldComment(fieldComment);\n        setOpenCommentAlert(true);\n    };\n    const getComment = (field)=>{\n        var _rawTraining_procedureFields_nodes, _rawTraining_procedureFields;\n        if (bufferFieldComment.length > 0) {\n            const fieldComment = bufferFieldComment.find((procedureField)=>procedureField.fieldId == field.id);\n            if (fieldComment) {\n                return fieldComment.comment;\n            }\n        }\n        const fieldComment = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : (_rawTraining_procedureFields_nodes = _rawTraining_procedureFields.nodes) === null || _rawTraining_procedureFields_nodes === void 0 ? void 0 : _rawTraining_procedureFields_nodes.find((procedureField)=>procedureField.customisedComponentFieldID == field.id);\n        return (fieldComment === null || fieldComment === void 0 ? void 0 : fieldComment.comment) || field.comment;\n    };\n    const handleSaveComment = ()=>{\n        if (!trainingID) {\n            const fieldComment = bufferFieldComment.filter((procedureField)=>procedureField.fieldId !== currentField.id);\n            setBufferFieldComment([\n                ...fieldComment,\n                {\n                    fieldId: currentField.id,\n                    comment: currentComment\n                }\n            ]);\n            setOpenCommentAlert(false);\n            return;\n        }\n        if (currentFieldComment) {\n            var _rawTraining_procedureFields;\n            updateCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        id: currentFieldComment.id,\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: currentField.id,\n                        comment: currentComment\n                    }\n                }\n            });\n            const nodes = rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_procedureFields = rawTraining.procedureFields) === null || _rawTraining_procedureFields === void 0 ? void 0 : _rawTraining_procedureFields.nodes;\n            if (nodes) {\n                setRawTraining({\n                    ...rawTraining,\n                    procedureFields: {\n                        ...rawTraining.procedureFields,\n                        nodes: [\n                            ...nodes.filter((procedureField)=>procedureField.customisedComponentFieldID !== currentField.id),\n                            {\n                                ...currentFieldComment,\n                                comment: currentComment\n                            }\n                        ]\n                    }\n                });\n            }\n        } else {\n            createCustomisedComponentFieldData({\n                variables: {\n                    input: {\n                        trainingSessionID: trainingID,\n                        customisedComponentFieldID: currentField.id,\n                        comment: currentComment\n                    }\n                }\n            });\n        }\n        setOpenCommentAlert(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            !training && trainingID > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.TrainingSessionFormSkeleton, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1334,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Name of trainer\",\n                        disabled: locked,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_dropdown_crew_dropdown__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                disabled: locked,\n                                offline: offline,\n                                value: training === null || training === void 0 ? void 0 : training.TrainerID,\n                                onChange: handleTrainerChange,\n                                memberIdOptions: memberIdOptions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1338,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                className: \"text-destructive\",\n                                children: hasFormErrors && formErrors.TrainerID\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1345,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1337,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Crew trained\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            value: training === null || training === void 0 ? void 0 : training.Members,\n                            onChange: handleMemberChange,\n                            memberIdOptions: memberIdOptions\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1350,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1349,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Training types\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-6 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_type_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    offline: offline,\n                                    value: training === null || training === void 0 ? void 0 : training.TrainingTypes,\n                                    onChange: handleTrainingTypeChange,\n                                    locked: locked\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1360,\n                                    columnNumber: 29\n                                }, undefined),\n                                formErrors.TrainingTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                    className: \"text-destructive mt-1\",\n                                    children: formErrors.TrainingTypes\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1367,\n                                    columnNumber: 33\n                                }, undefined),\n                                training && trainingTypes.filter((type)=>{\n                                    var _training_TrainingTypes;\n                                    return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                                }).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                    variant: \"primary\",\n                                    className: \"w-fit flex-1\",\n                                    onClick: ()=>setOpenViewProcedure(true),\n                                    children: \"View Procedures\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1379,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1359,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1358,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Location of training\",\n                        disabled: locked,\n                        children: vesselList && (rawTraining || trainingID === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                            options: vesselList || [],\n                            value: (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Vessel\" ? vesselList === null || vesselList === void 0 ? void 0 : vesselList.filter((vessel)=>+vessel.value === +(training === null || training === void 0 ? void 0 : training.VesselID))[0] : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Onshore\" ? {\n                                label: \"Desktop/shore\",\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType\n                            } : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Other\" ? {\n                                label: \"Other\",\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType\n                            } : (rawTraining === null || rawTraining === void 0 ? void 0 : rawTraining.trainingLocationType) === \"Location\" && (rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation === void 0 ? void 0 : _rawTraining_trainingLocation.id) > 0 ? {\n                                label: rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation1 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation1 === void 0 ? void 0 : _rawTraining_trainingLocation1.title,\n                                value: rawTraining === null || rawTraining === void 0 ? void 0 : (_rawTraining_trainingLocation2 = rawTraining.trainingLocation) === null || _rawTraining_trainingLocation2 === void 0 ? void 0 : _rawTraining_trainingLocation2.id\n                            } : (vesselList === null || vesselList === void 0 ? void 0 : vesselList.find((vessel)=>+vessel.value === +vesselId)) || null,\n                            onChange: handleTrainingVesselChange,\n                            placeholder: \"Select location\",\n                            isDisabled: true,\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1392,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1390,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        className: \"text-destructive\",\n                        children: hasFormErrors && formErrors.VesselID\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1438,\n                        columnNumber: 21\n                    }, undefined),\n                    displayField(\"CrewTraining_FuelLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Fuel level at end of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_22__.Input, {\n                            id: \"fuel-level\",\n                            name: \"fuel-level\",\n                            type: \"number\",\n                            defaultValue: training === null || training === void 0 ? void 0 : training.FuelLevel,\n                            className: \"w-full\",\n                            placeholder: \"Fuel level\",\n                            onChange: lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()(function(e) {\n                                setTraining({\n                                    ...training,\n                                    FuelLevel: e.target.value\n                                });\n                            }, 600)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1446,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1443,\n                        columnNumber: 25\n                    }, undefined),\n                    displayField(\"CrewTraining_StartTime\") || displayField(\"CrewTraining_FinishTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Training duration\",\n                        className: \"mb-1\",\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1464,\n                        columnNumber: 29\n                    }, undefined),\n                    displayField(\"CrewTraining_StartTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"Start time of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            time: startTime,\n                            handleTimeChange: handleStartTimeChange,\n                            timeID: \"startTime\",\n                            fieldName: \"Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1473,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1472,\n                        columnNumber: 25\n                    }, undefined),\n                    displayField(\"CrewTraining_FinishTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                        label: \"End time of training\",\n                        disabled: locked,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            time: finishTime,\n                            handleTimeChange: handleFinishTimeChange,\n                            timeID: \"finishTime\",\n                            fieldName: \"Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1484,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1483,\n                        columnNumber: 25\n                    }, undefined),\n                    (!currentEvent || training) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        id: \"TrainingSummary\",\n                        placeholder: \"Summary of training, identify any outcomes, further training required or other observations.\",\n                        handleEditorChange: handleEditorChange,\n                        content: content,\n                        disabled: locked\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1494,\n                        columnNumber: 25\n                    }, undefined),\n                    selectedMemberList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.H4, {\n                                children: \"Participant Signatures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1513,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                children: selectedMemberList.map((member, index)=>{\n                                    var _signatureMembers_find, _signatureMembers_find1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        title: member.label,\n                                        member: member.label,\n                                        memberId: member.value,\n                                        onSignatureChanged: onSignatureChanged,\n                                        signature: {\n                                            signatureData: (_signatureMembers_find = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find === void 0 ? void 0 : _signatureMembers_find.SignatureData,\n                                            id: (_signatureMembers_find1 = signatureMembers.find((sig)=>sig.MemberID === member.value)) === null || _signatureMembers_find1 === void 0 ? void 0 : _signatureMembers_find1.ID\n                                        },\n                                        locked: locked\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                        lineNumber: 1517,\n                                        columnNumber: 41\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1514,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end pb-4 pt-4 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                variant: \"back\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                                onClick: closeModal,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1546,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                                variant: \"primary\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                                onClick: locked ? ()=>{} : handleSave,\n                                disabled: mutationCreateTrainingSessionLoading || mutationUpdateTrainingSessionLoading,\n                                children: trainingID === 0 ? \"Save\" : \"Update\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1552,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                        lineNumber: 1545,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1336,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.Sheet, {\n                open: openViewProcedure,\n                onOpenChange: setOpenViewProcedure,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-3/4 sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetTitle, {\n                                children: \"Procedures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                lineNumber: 1569,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1568,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_25__.SheetBody, {\n                            children: training && trainingTypes.filter((type)=>{\n                                var _training_TrainingTypes;\n                                return (training === null || training === void 0 ? void 0 : (_training_TrainingTypes = training.TrainingTypes) === null || _training_TrainingTypes === void 0 ? void 0 : _training_TrainingTypes.includes(type.id)) && type.procedure;\n                            }).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-md p-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium leading-6 mb-4\",\n                                            children: type.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                            lineNumber: 1584,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            dangerouslySetInnerHTML: {\n                                                __html: type.procedure\n                                            }\n                                        }, type.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                            lineNumber: 1587,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, type.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                                    lineNumber: 1581,\n                                    columnNumber: 37\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                            lineNumber: 1571,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                    lineNumber: 1567,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n                lineNumber: 1566,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\crew-training-event.tsx\",\n        lineNumber: 1332,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingEvent, \"IFIVVo0IGCE+7GWuQeWlOiL77ts=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation\n    ];\n});\n_c = CrewTrainingEvent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingEvent);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingEvent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\n"));

/***/ })

});