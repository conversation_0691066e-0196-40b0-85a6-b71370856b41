'use client'

import React, { useRef, useEffect, useCallback, useMemo, useState } from 'react'
import SignatureCanvas from 'react-signature-canvas'
import type SignaturePadType from 'react-signature-canvas'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Eraser, Pen, Lock } from 'lucide-react'
import { H4, Label, P } from './ui'
import { cn } from '@/app/lib/utils'
import { getSignatureUrl } from '@/app/lib/actions'

interface SignatureData {
    signatureData?: string
    id?: number
    [key: string]: any
}

interface SignaturePadProps {
    signature?: SignatureData
    member?: string
    memberId?: number
    title?: string
    onSignatureChanged: (
        signature: string,
        member?: string,
        memberId?: number,
    ) => void
    penColor?: string
    className?: string
    description?: string
    locked?: boolean
}

const SignaturePad: React.FC<SignaturePadProps> = React.memo(
    ({
        signature,
        member,
        memberId,
        title,
        onSignatureChanged,
        penColor = 'blue',
        className,
        description,
        locked = false,
        ...canvasProps
    }) => {
        const padRef = useRef<SignaturePadType>(null)
        const [dataUrl, setDataUrl] = useState<string | null>(
            signature?.signatureData ?? null,
        )

        // 1. If we have a valid ID but no inline data, fetch it
        useEffect(() => {
            if (signature?.id && signature.id > 0 && !signature.signatureData) {
                getSignatureUrl(signature.id)
                    .then(setDataUrl)
                    .catch((err) => console.error('Fetch sig URL failed:', err))
            }
        }, [signature?.id, signature?.signatureData])

        // 2. Whenever dataUrl updates, load it into the pad
        useEffect(() => {
            const pad = padRef.current
            if (pad && dataUrl) {
                pad.clear()
                pad.fromDataURL(dataUrl)
            }
        }, [dataUrl])

        const handleEnd = useCallback(() => {
            const pad = padRef.current
            if (pad && !pad.isEmpty()) {
                const url = pad.toDataURL()
                onSignatureChanged(url, member, memberId)
            }
        }, [onSignatureChanged, member, memberId])

        const handleClear = useCallback(() => {
            const pad = padRef.current
            pad?.clear()
            onSignatureChanged('', member, memberId)
            setDataUrl(null)
        }, [onSignatureChanged, member, memberId])

        const displayTitle = useMemo(
            () => title ?? member ?? 'Signature',
            [title, member],
        )

        return (
            <div className={cn('relative w-full space-y-2', className)}>
                {locked && (
                    <Badge
                        variant="destructive"
                        className="absolute top-2 right-2 text-xs gap-1">
                        <Lock className="h-3 w-3" />
                        Locked
                    </Badge>
                )}

                <div>
                    {/*<H4>{displayTitle}</H4>*/}
                    {description && (
                        <P className="text-sm text-muted-foreground">
                            {description}
                        </P>
                    )}
                </div>

                <Label htmlFor="sig-canvas" label={displayTitle}>
                    <SignatureCanvas
                        {...canvasProps}
                        ref={padRef}
                        penColor={penColor}
                        canvasProps={{
                            id: 'sig-canvas',
                            className: cn(
                                'border-2 border-dashed border-outer-space-400 rounded-lg h-48',
                                locked ? 'bg-muted/50' : 'bg-white',
                            ),
                            style: {
                                width: '100%',
                                touchAction: locked ? 'none' : 'auto',
                                cursor: locked ? 'not-allowed' : 'crosshair',
                            },
                        }}
                        onEnd={handleEnd}
                    />
                </Label>

                {locked && (
                    // an overlay to prevent drawing when locked
                    <div className="absolute inset-0 bg-white/60 rounded-lg pointer-events-none" />
                )}

                <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Pen className="h-4 w-4" />
                        Draw your signature
                    </div>
                    <Button
                        size="sm"
                        iconLeft={Eraser}
                        className="w-fit"
                        onClick={handleClear}
                        disabled={locked}
                        aria-label="Clear signature">
                        Clear
                    </Button>
                </div>
            </div>
        )
    },
)

export default SignaturePad
