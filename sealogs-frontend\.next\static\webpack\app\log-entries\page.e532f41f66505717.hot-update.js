"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/signature-pad.tsx":
/*!******************************************!*\
  !*** ./src/components/signature-pad.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_signature_canvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-signature-canvas */ \"(app-pages-browser)/./node_modules/.pnpm/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1/node_modules/react-signature-canvas/build/index.js\");\n/* harmony import */ var react_signature_canvas__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_signature_canvas__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eraser.js\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SignaturePad = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = _s((param)=>{\n    let { signature, member, memberId, title, onSignatureChanged, penColor = \"blue\", className, description, locked = false, ...canvasProps } = param;\n    _s();\n    // Debug the member data received by SignaturePad\n    console.log(\"SignaturePad received member:\", member, \"with ID:\", memberId);\n    const padRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var _signature_signatureData;\n    const [dataUrl, setDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_signature_signatureData = signature === null || signature === void 0 ? void 0 : signature.signatureData) !== null && _signature_signatureData !== void 0 ? _signature_signatureData : null);\n    // 1. If we have a valid ID but no inline data, fetch it\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ((signature === null || signature === void 0 ? void 0 : signature.id) && signature.id > 0 && !signature.signatureData) {\n            (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getSignatureUrl)(signature.id).then(setDataUrl).catch((err)=>console.error(\"Fetch sig URL failed:\", err));\n        }\n    }, [\n        signature === null || signature === void 0 ? void 0 : signature.id,\n        signature === null || signature === void 0 ? void 0 : signature.signatureData\n    ]);\n    // 2. Whenever dataUrl updates, load it into the pad\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const pad = padRef.current;\n        if (pad && dataUrl) {\n            pad.clear();\n            pad.fromDataURL(dataUrl);\n        }\n    }, [\n        dataUrl\n    ]);\n    const handleEnd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const pad = padRef.current;\n        if (pad && !pad.isEmpty()) {\n            const url = pad.toDataURL();\n            onSignatureChanged(url, member, memberId);\n        }\n    }, [\n        onSignatureChanged,\n        member,\n        memberId\n    ]);\n    const handleClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const pad = padRef.current;\n        pad === null || pad === void 0 ? void 0 : pad.clear();\n        onSignatureChanged(\"\", member, memberId);\n        setDataUrl(null);\n    }, [\n        onSignatureChanged,\n        member,\n        memberId\n    ]);\n    const displayTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _ref;\n        return (_ref = title !== null && title !== void 0 ? title : member) !== null && _ref !== void 0 ? _ref : \"Signature\";\n    }, [\n        title,\n        member\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative w-full space-y-2\", className),\n        children: [\n            locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                variant: \"destructive\",\n                className: \"absolute top-2 right-2 text-xs gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Locked\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 102,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui__WEBPACK_IMPORTED_MODULE_5__.P, {\n                    className: \"text-sm text-muted-foreground\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 110,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                htmlFor: \"sig-canvas\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_signature_canvas__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    ...canvasProps,\n                    ref: padRef,\n                    penColor: penColor,\n                    canvasProps: {\n                        id: \"sig-canvas\",\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"border-2 border-dashed border-outer-space-400 rounded-lg h-48\", locked ? \"bg-muted/50\" : \"bg-white\"),\n                        style: {\n                            width: \"100%\",\n                            touchAction: locked ? \"none\" : \"auto\",\n                            cursor: locked ? \"not-allowed\" : \"crosshair\"\n                        }\n                    },\n                    onEnd: handleEnd\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 119,\n                columnNumber: 17\n            }, undefined),\n            locked && // an overlay to prevent drawing when locked\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white/60 rounded-lg pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 142,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 25\n                            }, undefined),\n                            \"Draw your signature\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        size: \"sm\",\n                        iconLeft: _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        className: \"w-fit\",\n                        onClick: handleClear,\n                        disabled: locked,\n                        \"aria-label\": \"Clear signature\",\n                        children: \"Clear\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 145,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n        lineNumber: 100,\n        columnNumber: 13\n    }, undefined);\n}, \"TJjvpSMRJ7e9XwkzR4OfCEZZTXM=\")), \"TJjvpSMRJ7e9XwkzR4OfCEZZTXM=\");\n_c1 = SignaturePad;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SignaturePad);\nvar _c, _c1;\n$RefreshReg$(_c, \"SignaturePad$React.memo\");\n$RefreshReg$(_c1, \"SignaturePad\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/signature-pad.tsx\n"));

/***/ })

});