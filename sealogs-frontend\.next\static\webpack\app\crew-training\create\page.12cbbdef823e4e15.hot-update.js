"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/components/signature-pad.tsx":
/*!******************************************!*\
  !*** ./src/components/signature-pad.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_signature_canvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-signature-canvas */ \"(app-pages-browser)/./node_modules/.pnpm/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1/node_modules/react-signature-canvas/build/index.js\");\n/* harmony import */ var react_signature_canvas__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_signature_canvas__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eraser.js\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SignaturePad = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = _s((param)=>{\n    let { signature, member, label, memberId, title, onSignatureChanged, penColor = \"blue\", className, description, locked = false, ...canvasProps } = param;\n    _s();\n    const padRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var _signature_signatureData;\n    const [dataUrl, setDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_signature_signatureData = signature === null || signature === void 0 ? void 0 : signature.signatureData) !== null && _signature_signatureData !== void 0 ? _signature_signatureData : null);\n    // 1. If we have a valid ID but no inline data, fetch it\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ((signature === null || signature === void 0 ? void 0 : signature.id) && signature.id > 0 && !signature.signatureData) {\n            (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getSignatureUrl)(signature.id).then(setDataUrl).catch((err)=>console.error(\"Fetch sig URL failed:\", err));\n        }\n    }, [\n        signature === null || signature === void 0 ? void 0 : signature.id,\n        signature === null || signature === void 0 ? void 0 : signature.signatureData\n    ]);\n    // 2. Whenever dataUrl updates, load it into the pad\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const pad = padRef.current;\n        if (pad && dataUrl) {\n            pad.clear();\n            pad.fromDataURL(dataUrl);\n        }\n    }, [\n        dataUrl\n    ]);\n    const handleEnd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const pad = padRef.current;\n        if (pad && !pad.isEmpty()) {\n            const url = pad.toDataURL();\n            onSignatureChanged(url, member, memberId);\n        }\n    }, [\n        onSignatureChanged,\n        member,\n        memberId\n    ]);\n    const handleClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const pad = padRef.current;\n        pad === null || pad === void 0 ? void 0 : pad.clear();\n        onSignatureChanged(\"\", member, memberId);\n        setDataUrl(null);\n    }, [\n        onSignatureChanged,\n        member,\n        memberId\n    ]);\n    const displayTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _ref;\n        return (_ref = title !== null && title !== void 0 ? title : member) !== null && _ref !== void 0 ? _ref : \"Signature\";\n    }, [\n        title,\n        member\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative w-full space-y-2\", className),\n        children: [\n            locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                variant: \"destructive\",\n                className: \"absolute top-2 right-2 text-xs gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Locked\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 96,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui__WEBPACK_IMPORTED_MODULE_5__.P, {\n                    className: \"text-sm text-muted-foreground\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 104,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                htmlFor: \"sig-canvas\",\n                label: label !== null && label !== void 0 ? label : displayTitle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_signature_canvas__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    ...canvasProps,\n                    ref: padRef,\n                    penColor: penColor,\n                    canvasProps: {\n                        id: \"sig-canvas\",\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"border-2 border-dashed border-outer-space-400 rounded-lg h-48\", locked ? \"bg-muted/50\" : \"bg-white\"),\n                        style: {\n                            width: \"100%\",\n                            touchAction: locked ? \"none\" : \"auto\",\n                            cursor: locked ? \"not-allowed\" : \"crosshair\"\n                        }\n                    },\n                    onEnd: handleEnd\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 113,\n                columnNumber: 17\n            }, undefined),\n            locked && // an overlay to prevent drawing when locked\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white/60 rounded-lg pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 136,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 25\n                            }, undefined),\n                            \"Draw your signature\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        size: \"sm\",\n                        iconLeft: _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        className: \"w-fit\",\n                        onClick: handleClear,\n                        disabled: locked,\n                        \"aria-label\": \"Clear signature\",\n                        children: \"Clear\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 139,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n        lineNumber: 94,\n        columnNumber: 13\n    }, undefined);\n}, \"TJjvpSMRJ7e9XwkzR4OfCEZZTXM=\")), \"TJjvpSMRJ7e9XwkzR4OfCEZZTXM=\");\n_c1 = SignaturePad;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SignaturePad);\nvar _c, _c1;\n$RefreshReg$(_c, \"SignaturePad$React.memo\");\n$RefreshReg$(_c1, \"SignaturePad\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/signature-pad.tsx\n"));

/***/ })

});