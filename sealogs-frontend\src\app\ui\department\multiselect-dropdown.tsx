import { useEffect, useState } from 'react'
import { isEmpty } from 'lodash'
import { useLazyQuery } from '@apollo/client'

import { READ_ONE_SEALOGS_MEMBER } from '@/app/lib/graphQL/query'
import { isAdmin } from '@/app/helpers/userHelper'
import { Combobox } from '@/components/ui/comboBox'

const DepartmentMultiSelectDropdown = ({
    value = [], // an array of department IDs
    onChange,
    allDepartments,
}: {
    value: any[]
    onChange: any
    allDepartments: any
}) => {
    const [departmentList, setDepartmentList] = useState([] as any)
    const [selectedIDs, setSelectedIDs] = useState([] as any)
    const [error, setError] = useState<any>(false)
    const [currentDepartment, setCurrentDepartment] = useState<any>(false)

    const [querySeaLogsMembers] = useLazyQuery(READ_ONE_SEALOGS_MEMBER, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneSeaLogsMember
            if (data) {
                setCurrentDepartment(data.departments.nodes)
            }
        },
        onError: (error: any) => {
            console.error('querySeaLogsMembers error', error)
        },
    })

    useEffect(() => {
        querySeaLogsMembers({
            variables: {
                filter: { id: { eq: +(localStorage.getItem('userId') ?? 0) } },
            },
        })
    }, [])

    const handleSetDepartmentList = (allDepartment: any) => {
        if (allDepartment) {
            const departments = allDepartment?.map((item: any) => {
                return {
                    value: item.id,
                    label: item.title,
                }
            })
            setDepartmentList(departments)
        }
    }
    // getDepartmentList(handleSetDepartmentList)

    const handleOnChange = (data: any) => {
        onChange(data)
        setSelectedIDs(data)
    }

    useEffect(() => {
        if (!isEmpty(departmentList) && !isEmpty(value)) {
            setSelectedIDs(value)
        }
    }, [value, departmentList])

    useEffect(() => {
        handleSetDepartmentList(allDepartments)
    }, [])

    return (
        <Combobox
            value={
                selectedIDs
                    ? selectedIDs
                          .map((id: any) => {
                              const department = departmentList.find(
                                  (c: any) => c.value === id,
                              )
                              return department
                          })
                          .filter((department: any) => department !== undefined)
                    : []
            }
            multi
            options={
                isAdmin()
                    ? departmentList
                    : currentDepartment
                      ? departmentList.filter((item: any) =>
                            currentDepartment.some(
                                (c: any) => c.id === item.value,
                            ),
                        )
                      : departmentList
            }
            onChange={handleOnChange}
            placeholder="Select Department"
            isLoading={!departmentList}
        />
    )
}

export default DepartmentMultiSelectDropdown
