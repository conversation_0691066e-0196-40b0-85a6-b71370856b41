"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/weather/forecast.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _logbook_components_location__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logbook/components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/weatherHelper */ \"(app-pages-browser)/./src/app/helpers/weatherHelper.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _forecast_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forecast-list */ \"(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_barometer_widget__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./widgets/barometer-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/barometer-widget.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecast = (param)=>{\n    let { logBookEntryID, offline = false, locked } = param;\n    _s();\n    const [isWriteModeForecast, setIsWriteModeForecast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isManualEntry, setIsManualEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forecast, setForecast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Current location state for the location field\n    const [currentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: null,\n        longitude: null\n    });\n    const [selectedCoordinates, setSelectedCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: null,\n        longitude: null\n    });\n    const isOnline = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_24__.useOnline)();\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const [isStormGlassLoading, setIsStormGlassLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshList, setRefreshList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [geoLocations, setGeoLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getTimeNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"HH:mm\");\n    };\n    const [getGeoLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_12__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readGeoLocations.nodes;\n            if (data) {\n                setGeoLocations(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryGeoLocations error\", error);\n        }\n    });\n    const getDayNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\");\n    };\n    const initForecast = ()=>{\n        setForecast({\n            id: 0,\n            time: getTimeNow(),\n            day: getDayNow(),\n            geoLocationID: 0,\n            lat: 0,\n            long: 0,\n            logBookEntryID: logBookEntryID\n        });\n    };\n    const createForecast = ()=>{\n        initForecast();\n        setIsWriteModeForecast(true);\n    };\n    const handleSetCurrentLocation = (value)=>{\n        setForecast({\n            ...forecast,\n            geoLocationID: 0,\n            lat: value.latitude,\n            long: value.longitude\n        });\n        setSelectedCoordinates({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setForecast({\n                ...forecast,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, use them directly\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setSelectedCoordinates({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            } else {\n                // Otherwise find the location in geoLocations by ID\n                geoLocations.find((item)=>{\n                    if (item.id == +value.value) {\n                        setSelectedCoordinates({\n                            latitude: item.lat,\n                            longitude: item.long\n                        });\n                        return true;\n                    }\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setForecast({\n                ...forecast,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update selected coordinates\n            setSelectedCoordinates({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    };\n    const processStormGlassData = (data)=>{\n        const { windSpeed, windDirection, swellHeight, visibility, precipitation, pressure, cloudCover } = data.hours[0];\n        const windSpeedInKnots = (windSpeed ? (windSpeed.noaa || windSpeed.sg || 0) / 0.51444 : 0).toFixed(0) // Convert m/s to knot. One knot is equal to approximately 0.51444 meters per second (m/s).\n        ;\n        const compassWindDirection = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getWindDirection)(windDirection ? windDirection.noaa || windDirection.sg || 0 : 0) // convert degrees to compass direction\n        ;\n        const swellValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getSwellHeightRange)(swellHeight ? swellHeight.noaa || swellHeight.sg || 0 : 0);\n        const visibilityValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getVisibility)(visibility ? visibility.noaa || visibility.sg || 0 : 0);\n        const precipitationValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getPrecipitation)(precipitation ? precipitation.noaa || precipitation.sg || 0 : 0);\n        const pressureValue = pressure ? pressure.noaa || pressure.sg || 0 : 0;\n        const cloudCoverValue = (cloudCover ? cloudCover.noaa || cloudCover.sg || 0 : 0).toFixed(0);\n        setForecast({\n            ...forecast,\n            windSpeed: +windSpeedInKnots,\n            windDirection: compassWindDirection,\n            swell: swellValue,\n            visibility: visibilityValue,\n            precipitation: precipitationValue,\n            pressure: +pressureValue,\n            cloudCover: +cloudCoverValue\n        });\n        setIsStormGlassLoading(false);\n    };\n    const isStormGlassButtonEnabled = ()=>{\n        let isStormGlassButtonEnabled = false;\n        if (+forecast.geoLocationID > 0) {\n            isStormGlassButtonEnabled = true;\n        } else if (!isNaN(+forecast.lat) || !isNaN(+forecast.long)) {\n            isStormGlassButtonEnabled = true;\n        }\n        if (!isOnline) {\n            isStormGlassButtonEnabled = false;\n        }\n        return isStormGlassButtonEnabled;\n    };\n    const getStormGlassData = ()=>{\n        setIsManualEntry(false);\n        if (forecast.geoLocationID > 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.loading(\"Retrieving forecast...\");\n            setIsStormGlassLoading(true);\n            const dateString = \"\".concat(forecast.day, \" \").concat(forecast.time);\n            let startDate = new Date(dateString);\n            let endDate = startDate;\n            var headers = {\n                \"Cache-Control\": \"no-cache\",\n                Authorization: \"480c5714-38bc-11ea-acb4-0242ac130002-480c58fe-38bc-11ea-acb4-0242ac130002\" || 0,\n                \"Access-Control-Allow-Credentials\": \"true\"\n            };\n            var params = \"windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover\";\n            const url = \"https://api.stormglass.io/v2/weather/point?lat=\".concat(selectedCoordinates.latitude || 0, \"&lng=\").concat(selectedCoordinates.longitude || 0, \"&params=\").concat(params, \"&start=\").concat(startDate.toISOString(), \"&end=\").concat(endDate.toISOString());\n            let request = fetch(url, {\n                method: \"GET\",\n                headers\n            });\n            request.then((response)=>response.json()).then((jsonData)=>{\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Forecast retrieved successfully\");\n                processStormGlassData(jsonData);\n            }).catch((error)=>{\n                setIsStormGlassLoading(false);\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                console.error(\"Catch error:\", error);\n            });\n            return request;\n        } else {\n            if (\"geolocation\" in navigator) {\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.loading(\"Retrieving forecast...\");\n                setIsStormGlassLoading(true);\n                return new Promise((resolve, reject)=>{\n                    return navigator.geolocation.getCurrentPosition(()=>{\n                        const dateString = \"\".concat(forecast.day, \" \").concat(forecast.time);\n                        let startDate = new Date(dateString);\n                        let endDate = startDate;\n                        var headers = {\n                            \"Cache-Control\": \"no-cache\",\n                            Authorization: \"480c5714-38bc-11ea-acb4-0242ac130002-480c58fe-38bc-11ea-acb4-0242ac130002\" || 0,\n                            \"Access-Control-Allow-Credentials\": \"true\"\n                        };\n                        var params = \"windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover\";\n                        const url = \"https://api.stormglass.io/v2/weather/point?lat=\".concat(selectedCoordinates.latitude || 0, \"&lng=\").concat(selectedCoordinates.longitude || 0, \"&params=\").concat(params, \"&start=\").concat(startDate.toISOString(), \"&end=\").concat(endDate.toISOString());\n                        let request = fetch(url, {\n                            method: \"GET\",\n                            headers\n                        });\n                        request.then((response)=>response.json()).then((jsonData)=>{\n                            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Forecast retrieved successfully\");\n                            processStormGlassData(jsonData);\n                            resolve(jsonData);\n                        }).catch((error)=>{\n                            setIsStormGlassLoading(false);\n                            reject(error);\n                            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                            console.error(\"Catch error:\", error);\n                        });\n                        return request;\n                    }, (error)=>{\n                        setIsStormGlassLoading(false);\n                        reject(error);\n                        sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                        console.error(\"Geolocation error\", error);\n                    });\n                });\n            } else {\n                setIsStormGlassLoading(false);\n                console.error(\"Geolocation is not supported by your browser\");\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"Geolocation is not supported by your browser\");\n            }\n        }\n    };\n    const handleOnChangePressure = (value)=>{\n        const pressure = Array.isArray(value) ? value[0] : value;\n        setForecast({\n            ...forecast,\n            pressure: pressure\n        });\n    };\n    const handleOnChangeSeaSwell = (item)=>{\n        if (item) {\n            setForecast({\n                ...forecast,\n                swell: item.value\n            });\n        }\n    };\n    const handleWindChange = (values)=>{\n        // Update both wind speed and direction in the forecast state\n        setForecast({\n            ...forecast,\n            windSpeed: values.speed,\n            windDirection: values.direction.value\n        });\n    };\n    const handleSetComment = lodash_debounce__WEBPACK_IMPORTED_MODULE_5___default()((item)=>{\n        setForecast({\n            ...forecast,\n            comment: item\n        });\n    }, 600);\n    const [createWeatherForecast, { loading: createWeatherForecastLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CreateWeatherForecast, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"CreateWeatherForecast Error\", error);\n        }\n    });\n    const [updateWeatherForecast, { loading: updateWeatherForecastLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast Error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        if (+forecast.id === 0) {\n            if (offline) {\n                await forecastModel.save({\n                    ...forecast,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n                });\n                setIsWriteModeForecast(false);\n                setRefreshList(true);\n            } else {\n                await createWeatherForecast({\n                    variables: {\n                        input: {\n                            ...forecast\n                        }\n                    }\n                });\n            }\n        } else {\n            if (forecast.geoLocation) delete forecast.geoLocation;\n            if (forecast.__typename) delete forecast.__typename;\n            if (offline) {\n                await forecastModel.save({\n                    ...forecast,\n                    day: dayjs__WEBPACK_IMPORTED_MODULE_2___default()(forecast.day).format(\"YYYY-MM-DD\")\n                });\n                setIsWriteModeForecast(false);\n                setRefreshList(true);\n            } else {\n                await updateWeatherForecast({\n                    variables: {\n                        input: {\n                            ...forecast\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const handleCancel = ()=>{\n        initForecast();\n        setIsWriteModeForecast(false);\n    };\n    const handleForecastClick = (forecast)=>{\n        if (locked) {\n            return;\n        }\n        setIsManualEntry(false);\n        const newForecast = {\n            ...forecast,\n            time: formatTime(forecast.time),\n            day: dayjs__WEBPACK_IMPORTED_MODULE_2___default()(forecast.day.toString()).format(\"YYYY-MM-DD\")\n        };\n        setForecast(newForecast);\n        setIsWriteModeForecast(true);\n    };\n    const [deleteWeatherForecasts, { loading: deleteWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DeleteWeatherForecasts, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"DeleteWeatherForecasts Error\", error);\n        }\n    });\n    const handleDeleteForecast = async ()=>{\n        if (offline) {\n            forecastModel.delete(forecast);\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        } else {\n            await deleteWeatherForecasts({\n                variables: {\n                    ids: [\n                        forecast.id\n                    ]\n                }\n            });\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const handleOnChangeCloudWidget = (item)=>{\n        setForecast({\n            ...forecast,\n            visibility: item.visibility.value,\n            precipitation: item.precipitation.value,\n            cloudCover: item.cloudCover\n        });\n    };\n    // Format time string for display\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logBookEntryID > 0) {\n            setForecast({\n                ...forecast,\n                logBookEntryID: logBookEntryID\n            });\n        }\n        getGeoLocations();\n    }, [\n        logBookEntryID\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_18__.Card, {\n        children: [\n            !isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.H2, {\n                                children: \"Weather forecast\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                                children: \"You can start by retrieving a weather forecast up to 7-days into the future. SeaLogs currently using the Stormglass API with more forecasting services coming soon.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                                children: \"After retrieving a forecast you can add your own observations. We use this data to compare the accuracy of forecasts plus share weather observations with our community of users.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                            disabled: locked,\n                            onClick: ()=>createForecast(),\n                            children: \"Add another forecast\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true),\n            isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            id: \"forecast-date\",\n                                            name: \"forecast-date\",\n                                            label: \"Date and time of forecast required\",\n                                            value: forecast.day && forecast.time ? new Date(\"\".concat(forecast.day, \"T\").concat(forecast.time)) : undefined,\n                                            mode: \"single\",\n                                            type: \"datetime\" // Keep datetime to include time picker\n                                            ,\n                                            onChange: (date)=>{\n                                                if (date) {\n                                                    const newDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date);\n                                                    setForecast({\n                                                        ...forecast,\n                                                        day: newDate.format(\"YYYY-MM-DD\"),\n                                                        time: newDate.format(\"HH:mm:00\")\n                                                    });\n                                                }\n                                            },\n                                            dateFormat: \"dd MMM,\",\n                                            timeFormat: \"HH:mm\" // Explicitly set time format\n                                            ,\n                                            placeholder: \"Time\",\n                                            closeOnSelect: false,\n                                            clearable: true,\n                                            icon: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                            className: \"w-full\",\n                                            includeTime: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2 flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            label: \"Location for forecast\",\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_components_location__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    offline: offline,\n                                                    setCurrentLocation: handleSetCurrentLocation,\n                                                    handleLocationChange: (e)=>{\n                                                        handleLocationChange(e);\n                                                    },\n                                                    currentEvent: {\n                                                        geoLocationID: forecast.geoLocationID,\n                                                        lat: forecast.lat,\n                                                        long: forecast.long\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    isWriteModeForecast && isStormGlassButtonEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            onClick: ()=>getStormGlassData(),\n                                            disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                            className: \"w-full\",\n                                            children: isStormGlassLoading ? \"Retrieving forecast...\" : \"Retrieve forecast (API)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            variant: \"secondary\",\n                                            onClick: ()=>setIsManualEntry(true),\n                                            className: \"w-full\",\n                                            children: \"OR enter a manual forecast (observation)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: isManualEntry ? \"flex flex-col gap-4\" : \"grid grid-cols-2 gap-2 sm:grid-cols-4 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        speed: forecast.windSpeed,\n                                        direction: forecast.windDirection,\n                                        onChange: handleWindChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        visibilityValue: forecast.visibility,\n                                        precipitationValue: forecast.precipitation,\n                                        cloudCoverValue: forecast.cloudCover,\n                                        onChange: handleOnChangeCloudWidget\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        value: forecast.swell,\n                                        onChange: handleOnChangeSeaSwell\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_barometer_widget__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        value: forecast.pressure,\n                                        editMode: isManualEntry,\n                                        onChange: handleOnChangePressure\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 25\n                            }, undefined),\n                            !isManualEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-[10px]\",\n                                children: \"Forecast provided by Stormglass API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    label: \"Your comments\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__.Textarea, {\n                                        id: \"forecast-comment\",\n                                        rows: 4,\n                                        placeholder: \"Comments ...\",\n                                        defaultValue: forecast.comment || \"\",\n                                        onChange: (e)=>handleSetComment(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"flex flex-col-reverse standard:flex-row gap-2.5 sm:justify-end mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                        variant: \"back\",\n                                        className: \"w-full standard:w-fit\",\n                                        onClick: handleCancel,\n                                        disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    +forecast.id > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                        variant: \"destructive\",\n                                        className: \"w-full standard:w-fit\",\n                                        iconLeft: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                        onClick: ()=>{\n                                            setDeleteDialogOpen(true);\n                                        },\n                                        disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                        children: \"Delete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                iconLeft: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                onClick: handleSave,\n                                disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                children: \"\".concat(+forecast.id === 0 ? \"Save\" : \"Update\", \" forecast\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 518,\n                columnNumber: 17\n            }, undefined),\n            !isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forecast_list__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    logBookEntryID: logBookEntryID,\n                    refreshList: refreshList,\n                    onClick: handleForecastClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                    lineNumber: 717,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 716,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_14__.AlertDialogNew, {\n                openDialog: deleteDialogOpen,\n                setOpenDialog: setDeleteDialogOpen,\n                handleCreate: handleDeleteForecast,\n                title: \"Delete Forecast Data\",\n                description: \"Are you sure you want to delete the forecast data for \".concat(forecast.time ? formatTime(forecast.time) : \"\", \" / \").concat(forecast.day ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_11__.formatDate)(forecast.day) : \"\", \"?\"),\n                variant: \"danger\",\n                actionText: \"Delete\",\n                cancelText: \"Cancel\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 726,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n        lineNumber: 490,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecast, \"7Fqh81ICgSbMHVlaGs6jrH67kdg=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_24__.useOnline,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation\n    ];\n});\n_c = WeatherForecast;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecast);\nvar _c;\n$RefreshReg$(_c, \"WeatherForecast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast.tsx\n"));

/***/ })

});